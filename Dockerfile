FROM --platform=linux/amd64 registry.228.by/sola:node-12 as buildContainer
WORKDIR /app
COPY . ./
RUN npm ci --quiet --unsafe-perm
RUN npm run build:ssr

FROM --platform=linux/amd64 registry.228.by/sola:node-12
WORKDIR /app
COPY --from=buildContainer /app/package.json /app
COPY --from=buildContainer /app/dist /app/dist
EXPOSE 4000
CMD envsubst < /app/dist/browser/env.template.js > /app/dist/browser/env.js && npm run serve:ssr
