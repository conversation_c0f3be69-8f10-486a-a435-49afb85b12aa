/* eslint-disable @typescript-eslint/no-require-imports */
import { APP_BASE_HREF } from '@angular/common';
import { ngExpressEngine } from '@nguniversal/express-engine';
import { REQUEST, RESPONSE } from '@nguniversal/express-engine/tokens';
import { applyDomino } from '@ntegral/ngx-universal-window';
import * as express from 'express';
import { NextFunction, Request, Response } from 'express';
import { existsSync } from 'fs';
import { join } from 'path';
import 'zone.js/dist/zone-node';
import { AppServerModule } from './src/main.server';

const BROWSER_DIR = join(process.cwd(), 'dist/browser');
applyDomino(global, join(BROWSER_DIR, 'index.html'));

// cache
const NodeCache = require('node-cache');
// stdTTL: (default: 0) the standard ttl as number in seconds for every generated cache element. 0 = unlimited
// checkperiod: (default: 600) The period in seconds, as a number, used for the automatic delete check interval. 0 = no periodic check.
const myCache = new NodeCache({ stdTTL: 5 * 60, checkperiod: 120 });

const cache = () => {
  return (req: Request, res: Response, next: NextFunction) => {
    const key = '__express__' + req.originalUrl || req.url;

    const exists = myCache.has(key);
    if (exists) {
      console.log(`from cache: `, req.originalUrl || req.url);
      const cachedBody = myCache.get(key);
      res.send(cachedBody);
      return;
    } else {
      // @ts-ignore 123
      res.sendResponse = res.send;
      // @ts-ignore 123
      res.send = (body: any) => {
        myCache.set(key, body);
        // @ts-ignore 123
        res.sendResponse(body);
      };
      next();
    }
  };
};

// The Express app is exported so that it can be used by serverless Functions.
export function app(): express.Express {
  // eslint-disable-next-line @typescript-eslint/dot-notation
  global['document'] = window.document;
  const server = express();
  const distFolder = join(process.cwd(), 'dist/browser');
  const indexHtml = existsSync(join(distFolder, 'index.original.html'))
    ? 'index.original.html'
    : 'index';

  const compression = require('compression');

  server.use(compression());
  // Our Universal express-engine (found @ https://github.com/angular/universal/tree/master/modules/express-engine)
  server.engine(
    'html',
    ngExpressEngine({
      bootstrap: AppServerModule,
      inlineCriticalCss: false,
    })
  );

  server.set('view engine', 'html');
  server.set('views', distFolder);
  server.enable('trust proxy');

  // Отдаем статические файлы из каталога 'dist/browser'
  server.use(
    express.static(distFolder, {
      index: false,
      maxAge: '1y',
      setHeaders: (res, path) => {
        res.setHeader('Cache-Control', 'public, max-age=31536000');
      },
    })
  );

  // Example Express Rest API endpoints
  // server.get('/api/**', (req, res) => { });
  // Serve static files from /browser
  server.get(
    '*.*',
    express.static(distFolder, {
      maxAge: '1y',
    })
  );

  //! ------------------- Turn off SSR for some routes ----------------------//

  server.get(
    ['/mc.yandex.ru/*', '/code.jivosite.com/*'],
    (req: express.Request, res: express.Response) => {
      res.sendFile(join(distFolder, 'index.html'));
    }
  );

  //! ---------------------------------------------------//

  // All regular routes use the Universal engine
  server.get('*', cache(), (req: express.Request, res: express.Response) => {
    if (req.originalUrl.slice(-1) === '/' && req.originalUrl.length > 1) {
      const slicedUrl = req.originalUrl.slice(0, -1);
      const fullUrl = req.protocol + '://' + req.get('host') + slicedUrl;
      res.redirect(301, fullUrl);
    } else {
      req.headers['hl'] = req.headers?.cookie?.slice(-2);
      req.headers['X-Forwarded-For'] = req.ip;

      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        Pragma: 'no-cache',
        Expires: '0',
      });

      res.render(indexHtml, {
        req,
        providers: [
          { provide: APP_BASE_HREF, useValue: req.baseUrl },
          { provide: REQUEST, useValue: req },
          { provide: RESPONSE, useValue: res },
        ],
      });
    }
  });

  return server;
}

function run(): void {
  const port = process.env['PORT'] || 4000;

  // Start up the Node server
  const server = app();
  server.listen(port, () => {
    console.log(`Node Express server listening on http://localhost:${port}`);
  });
}

// Webpack will replace 'require' with '__webpack_require__'
// '__non_webpack_require__' is a proxy to Node 'require'
// The below code is to ensure that the server is run only when not requiring the bundle.

// eslint-disable-next-line no-underscore-dangle
declare const __non_webpack_require__: NodeRequire;
const mainModule = __non_webpack_require__.main;
const moduleFilename = (mainModule && mainModule.filename) || '';
if (moduleFilename === __filename || moduleFilename.includes('iisnode')) {
  run();
}

export * from './src/main.server';
