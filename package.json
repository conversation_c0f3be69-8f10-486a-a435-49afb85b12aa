{"name": "yr-blog", "version": "0.0.0", "scripts": {"ng": "ng", "start": "npm run env && ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "prepare": "husky install", "lint": "ng lint", "linthtml": "npx htmlhint --config .htmlhintrc -i index.html [ENOENT]", "env": "envsub --env-file=.env src/environments/env.template.js src/environments/env.js", "start:ssr": "ng build && ng run yr-blog:server && npx nodenv --env=dist/browser/.env dist/server/main.js", "prettier:check": "prettier --config .prettierrc --check \"src/**/*.{ts,css,html}\"", "dev:ssr": "ng run yr-blog:serve-ssr", "serve:ssr": "node dist/server/main.js", "build:ssr": "ng build && ng run yr-blog:server", "prerender": "ng run yr-blog:prerender", "postinstall": "ngcc"}, "private": true, "dependencies": {"@angular/animations": "~13.3.0", "@angular/cdk": "~13.3.9", "@angular/common": "~13.3.0", "@angular/compiler": "~13.3.0", "@angular/core": "~13.3.0", "@angular/forms": "~13.3.0", "@angular/platform-browser": "~13.3.0", "@angular/platform-browser-dynamic": "~13.3.0", "@angular/platform-server": "~13.3.0", "@angular/router": "~13.3.0", "@fingerprintjs/fingerprintjs": "~3.3.1", "@ngneat/until-destroy": "^8.1.4", "@nguniversal/express-engine": "^13.1.1", "@ngx-translate/core": "~14.0.0", "@ngx-translate/http-loader": "~7.0.0", "@ntegral/ngx-universal-window": "~1.0.2", "@schematics/angular": "~13.3.9", "breakpoint-slicer": "~3.0.0-beta.1", "express": "^4.15.2", "focus-visible": "~5.2.0", "masonry-layout": "~4.2.2", "ngx-cookie": "~6.0.0", "ngx-cookie-backend": "~6.0.0", "ngx-masonry": "~13.0.0", "ngx-metafrenzy": "~8.1.0", "ngx-toastr": "~13.2.1", "node-cache": "~5.1.2", "node-env-run": "~4.0.2", "rxjs": "~7.5.0", "sha1": "~1.1.1", "tslib": "^2.3.0", "tua-body-scroll-lock": "~1.2.1", "util": "~0.12.4", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.3.9", "@angular-eslint/builder": "13.2.1", "@angular-eslint/eslint-plugin": "13.2.1", "@angular-eslint/eslint-plugin-template": "13.2.1", "@angular-eslint/schematics": "13.2.1", "@angular-eslint/template-parser": "13.2.1", "@angular/cli": "~13.3.9", "@angular/compiler-cli": "~13.3.0", "@nguniversal/builders": "^13.1.1", "@types/express": "^4.17.0", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "@types/sha1": "~1.1.3", "@typescript-eslint/eslint-plugin": "5.17.0", "@typescript-eslint/parser": "5.17.0", "envsub": "~4.0.7", "eslint": "^8.12.0", "eslint-plugin-import": "latest", "eslint-plugin-jsdoc": "latest", "eslint-plugin-prefer-arrow": "latest", "eslint-plugin-unused-imports": "~2.0.0", "htmllint": "~0.8.0", "husky": "^8.0.1", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "postcss-scss": "~4.0.4", "prettier": "^2.6.2", "stylelint": "~14.8.5", "stylelint-config-prettier": "~9.0.3", "stylelint-config-standard": "~25.0.0", "stylelint-order": "~5.0.0", "stylelint-scss": "~4.2.0", "typescript": "~4.6.2"}, "optionalDependencies": {"esbuild-android-arm64": "~0.14.22", "esbuild-darwin-64": "~0.14.22", "esbuild-darwin-arm64": "~0.14.22", "esbuild-freebsd-64": "~0.14.22", "esbuild-freebsd-arm64": "~0.14.22", "esbuild-linux-32": "~0.14.22", "esbuild-linux-64": "~0.14.22", "esbuild-linux-arm": "~0.14.22", "esbuild-linux-arm64": "~0.14.22", "esbuild-linux-mips64le": "~0.14.22", "esbuild-linux-ppc64le": "~0.14.22", "esbuild-linux-riscv64": "~0.14.22", "esbuild-linux-s390x": "~0.14.22", "esbuild-netbsd-64": "~0.14.22", "esbuild-openbsd-64": "~0.14.22", "esbuild-sunos-64": "~0.14.22", "esbuild-windows-32": "~0.14.22", "esbuild-windows-64": "~0.14.22", "esbuild-windows-arm64": "~0.14.22"}}