/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
  "compileOnSave": false,
  "compilerOptions": {
    "baseUrl": "./",
    "outDir": "./dist/out-tsc",
    "forceConsistentCasingInFileNames": true,
    "strict": true,
    "noImplicitOverride": true,
    "noPropertyAccessFromIndexSignature": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "sourceMap": true,
    "declaration": false,
    "downlevelIteration": true,
    "experimentalDecorators": true,
    "moduleResolution": "node",
    "importHelpers": true,
    "allowSyntheticDefaultImports": true,
    "skipLibCheck": true,
    "target": "es2017",
    "module": "es2020",
    "lib": ["es2019", "dom"],
    // "noUnusedLocals": true,
    // "noUnusedParameters": true,
    "paths": {
      "Env": ["src/environments/environment"],
      "Core/*": ["src/app/core/*"],
      "Shared/*": ["src/app/shared/*"],
      "Modules/*": ["src/app/modules/*"]
    }
  },
  "angularCompilerOptions": {
    "enableI18nLegacyMessageIdFormat": false,
    "strictInjectionParameters": true,
    "strictInputAccessModifiers": true,
    "strictTemplates": true
  }
}
