{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"yr-blog": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss", "skipTests": true}, "@schematics/angular:class": {"skipTests": true}, "@schematics/angular:directive": {"skipTests": true}, "@schematics/angular:guard": {"skipTests": true}, "@schematics/angular:interceptor": {"skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"allowedCommonJsDependencies": ["sha1"], "outputPath": "dist/browser", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets", {"glob": ".env", "input": ".", "output": "/"}, {"glob": "env?(.template).js", "input": "src/environments/", "output": "/"}], "styles": ["src/styles.scss"], "stylePreprocessorOptions": {"includePaths": ["src/styles"]}, "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "1mb", "maximumError": "2mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "40kb"}], "outputHashing": "all"}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"browserTarget": "yr-blog:build:production"}, "development": {"browserTarget": "yr-blog:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "yr-blog:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "server": {"builder": "@angular-devkit/build-angular:server", "options": {"outputPath": "dist/server", "main": "server.ts", "tsConfig": "tsconfig.server.json", "stylePreprocessorOptions": {"includePaths": ["src/styles"]}, "inlineStyleLanguage": "scss"}, "configurations": {"production": {"outputHashing": "media"}, "development": {"optimization": false, "sourceMap": true, "extractLicenses": false}}, "defaultConfiguration": "production"}, "serve-ssr": {"builder": "@nguniversal/builders:ssr-dev-server", "configurations": {"development": {"browserTarget": "yr-blog:build:development", "serverTarget": "yr-blog:server:development"}, "production": {"browserTarget": "yr-blog:build:production", "serverTarget": "yr-blog:server:production"}}, "defaultConfiguration": "development"}, "prerender": {"builder": "@nguniversal/builders:prerender", "options": {"routes": ["/"]}, "configurations": {"production": {"browserTarget": "yr-blog:build:production", "serverTarget": "yr-blog:server:production"}, "development": {"browserTarget": "yr-blog:build:development", "serverTarget": "yr-blog:server:development"}}, "defaultConfiguration": "production"}}}}, "defaultProject": "yr-blog"}