# Load environment-specific variables
ifneq (,$(wildcard ../.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT)))
    include ../.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT)
endif

include environment-variables.mk
include variables.mk
include executables.mk

export

deploy: publish-docker-compose-yml cleanup-dst-path upload-deployment-assets up telegram

publish-docker-compose-yml:
ifneq ("$(wildcard $(docker-compose-yml-template))","")
	@envsubst < $(docker-compose-yml-template) > docker-compose.yml
else
	$(error $(docker-compose-yml-template) not exists)
endif

cleanup-dst-path:
	$(call ssh-exec, rm -rf $(dst-path))

upload-deployment-assets:
	$(call ssh-exec, mkdir -p $(dst-path))
	$(call ssh-cp, docker-compose.yml)
	$(call ssh-cp, ../.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT))
	$(call ssh-exec, mv $(dst-path)/.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT) $(dst-path)/.env)

up:
	$(call ssh-exec, $(docker-compose) pull)
	$(call ssh-exec, $(docker-compose) up -d)

down:
	$(call ssh-exec, $(docker-compose) down --remove-orphans --volume)

telegram:
	$(call telegram-notify, $(BOT_MESSAGE))
