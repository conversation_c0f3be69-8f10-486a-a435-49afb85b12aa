version: '3.4'

services:
  app:
    image: ${APP_IMAGE}
    restart: unless-stopped
    env_file: .env
    extra_hosts:
      api.y-r.by: ${SERVER}
    logging:
      driver: "json-file"
      options:
        max-size: "100m"
    labels:
      - traefik.enable=true
      - traefik.http.middlewares.${REPO_BRANCH_SLUG}-redirect-secure.redirectscheme.scheme=https
      - traefik.http.middlewares.${REPO_BRANCH_SLUG}-redirect-secure.redirectscheme.permanent=true
      - traefik.http.routers.${REPO_BRANCH_SLUG}.rule=Host(`${APP_DOMAIN}`) || Host(`www.${APP_DOMAIN}`)
      - traefik.http.routers.${REPO_BRANCH_SLUG}.entrypoints=http
      - traefik.http.routers.${REPO_BRANCH_SLUG}-secure.rule=Host(`${APP_DOMAIN}`)
      - traefik.http.routers.${REPO_BRANCH_SLUG}-secure.tls.certresolver=myresolver
      - traefik.http.routers.${REPO_BRANCH_SLUG}-secure.tls=true
      - traefik.http.routers.${REPO_BRANCH_SLUG}-secure.entrypoints=https
      - traefik.http.services.${REPO_BRANCH_SLUG}-secure.loadbalancer.server.port=4000

networks:
  default:
    external:
      name: traefik
