import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { Environment } from 'Core/models';

declare global {
  /* eslint-disable no-var */
  var env: Environment;
  /* eslint-enable no-var */
}
const env = globalThis?.env || {
  production: process.env['PRODUCTION'],
  backendUrl: process.env['API_URL'],
  BUILD: process.env['BUILD'],
  locales: ['ru', 'be'],
  defaultLocale: 'ru',
};

export const environment: Environment = {
  production: coerceBooleanProperty(env.production),
  backendUrl: env.backendUrl || '',
  build: env.build,
  locales: ['ru', 'be'],
  defaultLocale: 'ru',
  metaDefaults: {
    title:
      'Интернет-магазин Ив Роше в Беларуси - <PERSON> Rocher каталог, купить французскую косметику и парфюмерию в Минске',
    description:
      'Ив Роше - это 100% активных ингредиентов растительного происхождения. &#10003; Скидки, подарки и акции! &#128666; Бесплатная доставка  от 49 рублей по Минску и всей Беларуси! &#10003; Наличный и безналичный расчет!',
  },
};

if (!environment.production) {
  // For easier debugging in development mode
  import('zone.js/plugins/zone-error' as any);
}
