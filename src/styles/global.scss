@use "core" as *;

@import "~ngx-toastr/toastr.css";

*:focus {
  outline: 0.3rem solid #000;
}

*.js-focus-visible :focus:not(.focus-visible) {
  outline: 0;
}

html {
  box-sizing: border-box;
  height: 100%;
  font-size: 62.5%;
  line-height: 1;
  -webkit-marquee-increment: calc(
    0vh
  ); // https://www.sarasoueidan.com/blog/safari-fluid-typography-bug-fix/

  *,
  *::before,
  *::after {
    box-sizing: inherit;
  }
}

body {
  display: flex;
  flex-direction: column;
  min-width: 320px;
  min-height: 100%;
  margin: 0;
  overflow-x: hidden;
  font-family: $font-family-primary;
  font-size: $font-size-base;
  color: $black;
  word-break: break-word;
  overflow-wrap: break-word;
  // background-color: $body-bg; // Instead this use <app-root>: https://www.tempertemper.net/blog/scroll-bounce-page-background-colour

  // Better cross browser font smoothing
  /* stylelint-disable-next-line value-keyword-case */
  text-rendering: optimizeLegibility;
  font-variant-ligatures: none;
  text-decoration-skip: objects;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-tap-highlight-color: transparent;
}

h1,
h2,
h3,
h4,
h5,
h6,
p {
  margin: 0;
}

b,
strong {
  font-weight: bolder;
}

small {
  font-size: 80%;
}

ul,
ol {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

dl,
dd,
figure,
blockquote {
  margin: 0;
}

table {
  border-collapse: collapse;
}

img,
input,
select,
textarea {
  max-width: 100%;
  height: auto;
}

iframe {
  max-width: 100%;
  border: 0;
}

audio,
video {
  display: block;
}

dialog {
  width: auto;
  padding: 0;
  overflow-y: auto;
  background-color: transparent;
  border: 0;
}

fieldset {
  min-width: 0;
  margin: 0;
  padding: 0.01em 0 0;
  border: 0;
}

button,
input,
optgroup,
select,
textarea {
  margin: 0;
  padding: 0;
  font: inherit;
  color: inherit;
  letter-spacing: inherit;
  background-color: transparent;
  border: 0;
  border-radius: 0;
  box-shadow: none;
}

input[placeholder] {
  text-overflow: ellipsis;
}

button,
input {
  overflow: visible;
}

button,
select {
  text-align: left;
  text-transform: none;
}

textarea {
  resize: vertical;
}

a {
  color: inherit;
  text-decoration: none;

  &:not([href]) {
    cursor: default;
  }
}

button,
[role="button"] {
  cursor: pointer;
}

[hidden] {
  display: none;
}

:disabled,
.disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.container {
  max-width: 124.8rem;
  margin: 0 auto;
}

.unselectable {
  user-select: none;
}

val-errors {
  display: block;
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
  color: #bd3b55;
}

.password-field {
  // remove on safari default icons
  &::-webkit-contacts-auto-fill-button,
  &::-webkit-credentials-auto-fill-button {
    visibility: hidden;
  }

  // remove on ms edge default icons
  &::-ms-reveal,
  &::-ms-clear {
    visibility: hidden;
  }
}

/* stylelint-disable-next-line selector-type-no-unknown */
tooltip {
  &.tooltip {
    padding: 6px;
    font-size: 12px;
    line-height: inherit;
    color: #2a2a2a;
    background-color: #fff;
    border-radius: 0;

    &.tooltip-bottom::after {
      border-color: transparent transparent #fff;
    }
  }
}

.toast-container {
  margin-top: 14.5rem;
}

.toast-container .ngx-toastr {
  background-color: #fff;
  box-shadow: 0 0.2rem 1rem 0 rgba(0, 0, 0, 0.13);
}

.toast-container .ngx-toastr.toast-success {
  color: #6f7e0d;
  background-image: url("/assets/img/success-icon.svg");
}

.toast-container .ngx-toastr.toast-error {
  color: #7e0d0d;
  background-image: url("/assets/img/error-icon.svg");
}

// Blog post page list styles

.blog-post-page-list-item {
  background-color: #fff;
  border-radius: 0.8rem;

  .blog-post-img-wrapper {
    @include maintain-ratio(2.24 1);

    @include to(s) {
      @include maintain-ratio(1.64 1);
    }
  }

  .page-list-item-info-wrapper {
    display: flex;
    flex-direction: column;
    padding: 0 2rem 2rem;

    @include to(s) {
      padding: 0 1.6rem;
    }

    .category-and-date {
      display: flex;
      width: 100%;
      margin-top: 1.6rem;

      .category,
      .date {
        font-size: 1.4rem;
        line-height: 1.43;
        font-weight: 500;
        vertical-align: bottom;
        color: #949494;
      }

      .date {
        margin-left: auto;
      }
    }
  }

  h2.blog-post-title {
    margin-top: 1.2rem;
    font-size: 2rem;
    line-height: 1.4;
    font-weight: 600;
    color: #232323;
  }

  .blog-post-description-text {
    margin-top: 2rem;
    padding: 0 2rem;
    line-height: 1.5;
    text-align: justify;
    color: #232323;

    @include to(s) {
      padding: 0 1.6rem;
    }

    &:not(:last-child) {
      margin-bottom: 2rem;

      @include to(s) {
        margin-bottom: 1.6rem;
      }
    }

    &:last-child {
      padding-bottom: 2rem;

      @include to(s) {
        padding-bottom: 1.6rem;
      }
    }
  }
}

// Pages list header styles

.contnent-from-body-html {
  display: flex;
  flex-direction: column;
  padding-bottom: 2.2rem;

  .banner-with-text {
    position: relative;
    width: 100%;

    img {
      width: 100%;
      user-select: none;
      pointer-events: none;
    }

    .banner-description {
      position: absolute;
      bottom: 23%;
      left: 0;
      width: 100%;
      padding: 0 3rem;
      font-size: 2rem;
      text-align: center;
      user-select: none;

      @include to(s) {
        font-size: 1.3rem;
      }

      &.white {
        color: #fff;
      }

      &.black {
        color: #000;
      }
    }
  }

  .category-date {
    margin: 2rem 0 1rem;
    padding: 0 3rem;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
    text-transform: uppercase;
  }

  .category-title {
    padding: 0 3rem;
    font-size: 5rem;
    font-weight: 500;
    text-align: center;

    @include to(s) {
      font-size: 3rem;
    }
  }

  .category-description {
    margin-top: 1rem;
    padding: 0 3rem;
    font-size: 2rem;
    font-weight: normal;
    text-align: center;

    @include to(s) {
      font-size: 1.8rem;
    }
  }
}

// Blog post styles

.blog-post-item {
  display: flex;
  flex-direction: column;
  padding: 4.5rem 5rem 0;

  @include to(l) {
    padding: 2rem 2.5rem 0;
  }

  @include to(s) {
    padding: 2rem 0 0;
  }

  .post-title {
    font-size: 3.4rem;
    font-weight: 500;
    text-align: center;

    @include to(s) {
      font-size: 2.3rem;
    }
  }

  .post-description {
    margin-top: 1rem;
    padding: 0 3.2rem;
    font-size: 2rem;
    font-weight: 300;

    @include to(s) {
      padding: 0 1.5rem;
      font-size: 1.2rem;
    }
  }

  .post-banner-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 1rem;

    picture {
      display: flex;
      flex: 0 0 100%;
      justify-content: center;
      align-items: center;
    }
  }

  .content-blocks-wrapper {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 5.5rem;

    @include to(s) {
      margin-top: 3.5rem;
    }
  }

  .content-block {
    display: flex;
    width: 100%;

    @include to(s) {
      flex-direction: column;
    }

    &:not(:last-child) {
      margin-bottom: 3rem;
    }

    .text-block,
    .img-wrapper,
    .beauty-advice-block {
      flex: 0 0 50%;
      padding: 0 1.5rem;
    }

    .text-block {
      @include to(s) {
        order: 1;
      }

      .heading {
        margin-bottom: 1.2rem;
        font-size: 2rem;
        font-weight: 500;
        text-align: center;

        @include to(s) {
          font-size: 1.5rem;
        }
      }

      p {
        padding-top: 0.5rem;
        font-size: 1.5rem;
        font-weight: 300;
        text-align: justify;

        @include to(s) {
          font-size: 1.3rem;
        }

        &:not(:last-child) {
          margin-bottom: 2.1rem;
        }

        a {
          color: #000;
          text-decoration: underline;
        }
      }
    }

    .img-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;

      @include to(s) {
        order: 2;
        margin-bottom: 2.1rem;
      }

      img {
        width: 100%;
        object-fit: contain;
      }
    }

    .beauty-advice-block {
      position: relative;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: 1.5rem 0;
      padding: 1.5rem 8.5rem;

      @include to(l) {
        padding: 1.5rem 5.5rem;
      }

      @include to(s) {
        order: 2;
      }

      &::before,
      &::after {
        content: "";
        position: absolute;
        width: 6rem;
        height: 6rem;
        background-repeat: no-repeat;
        background-position: center;
        background-size: contain;

        @include to(l) {
          width: 3rem;
          height: 3rem;
        }
      }

      &::before {
        top: 0;
        left: 1.5rem;
        background-image: url("/assets/img/blog-post/quotes_top.jpeg");
      }

      &::after {
        right: 1.5rem;
        bottom: 0;
        background-image: url("/assets/img/blog-post/quotes_bottom.jpeg");
      }

      .heading {
        margin-bottom: 1rem;
        font-size: 2rem;
        font-weight: 500;
        text-align: center;

        @include to(l) {
          font-size: 1.5rem;
        }
      }

      p {
        font-size: 2rem;
        font-weight: 300;
        text-align: justify;

        @include to(l) {
          font-size: 1.5rem;
        }

        @include to(s) {
          font-size: 1.3rem;
        }

        a {
          color: #000;

          &:hover {
            text-decoration: underline;
          }
        }

        &:not(:last-child) {
          margin-bottom: 1rem;
        }
      }
    }
  }

  .advice-block {
    margin: 0 1.5rem;
    padding: 3rem;
    border: 0.5rem solid #e1d5c4;

    @include to(s) {
      padding: 2rem;
    }

    .heading {
      font-size: 3rem;
      font-weight: 500;
      text-align: center;

      @include to(s) {
        font-size: 1.5rem;
      }
    }

    p {
      font-size: 2rem;
      font-weight: 300;
      text-align: center;

      @include to(s) {
        font-size: 1.3rem;
      }

      a {
        color: #000;

        &:hover {
          text-decoration: underline;
        }
      }

      .underline-text {
        text-decoration: underline;
      }
    }

    &:not(:last-child) {
      margin-bottom: 3rem;
    }
  }

  .video-wrapper {
    margin: 0 auto;

    &:not(:last-child) {
      margin-bottom: 3rem;
    }
  }

  .list-wrapper {
    display: flex;
    flex-direction: column;
    padding: 0 1.5rem;

    .heading {
      margin-bottom: 1.5rem;
      font-size: 2rem;
      font-weight: 500;
      text-align: center;

      @include to(s) {
        font-size: 1.5rem;
      }
    }

    .list {
      padding-left: 2rem;
      list-style: auto;

      @include to(s) {
        font-size: 1.3rem;
      }

      .list-item {
        font-size: 1.5rem;
        font-weight: 300;

        @include to(s) {
          font-size: 1.3rem;
        }

        a {
          color: #000;
          text-decoration: underline;
        }

        &:not(:last-child) {
          margin-bottom: 2.1rem;

          @include to(s) {
            margin-bottom: 1.1rem;
          }
        }
      }
    }

    &:not(:last-child) {
      margin-bottom: 3rem;
    }
  }
}

.beige {
  /* stylelint-disable-next-line declaration-no-important */
  color: #bc967c;
}
