@use "variables" as *;

@mixin ng-deep {
  ::ng-deep {
    @content;
  }
}

@mixin font-details($font-style, $font-weight, $font-size, $line-height) {
  font-size: $font-size;
  line-height: $line-height;
  font-weight: $font-weight;
  font-style: $font-style;
}

@mixin placeholder {
  &::placeholder {
    @content;
  }

  &:input-placeholder {
    @content;
  }

  &:placeholder {
    @content;
  }

  :input-placeholder {
    @content;
  }
}

@mixin maintain-ratio($ratio: 1 1) {
  $width: 100%;
  $height: percentage(nth($ratio, 2) / nth($ratio, 1));

  width: $width;
  height: 0;
  padding-bottom: $height;
}
