import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { Menu, MenuResponse } from '../models';

@Injectable({
  providedIn: 'root',
})
export class MenuService {
  menu$!: BehaviorSubject<Menu[]>;
  posts$!: BehaviorSubject<Menu[]>;

  private _menu!: Menu[];
  private _posts!: Menu[];

  get posts() {
    return this._posts;
  }

  set posts(value) {
    this._posts = value;
    this.posts$.next(value);
  }

  get menu() {
    return this._menu;
  }

  set menu(value) {
    this._menu = value;
    this.menu$.next(value);
  }

  constructor(private http: HttpClient) {
    this.menu$ = new BehaviorSubject<Menu[]>(this.menu);
    this.posts$ = new BehaviorSubject<Menu[]>(this.posts);
  }

  getMenu(name: string) {
    return this.http
      .get<MenuResponse[]>(`/menus/${name}`)
      .pipe(map(res => res.map(item => new Menu(item))));
  }
}
