import { isPlatformServer } from '@angular/common';
import { Inject, Injectable, PLATFORM_ID } from '@angular/core';
import FingerprintJS from '@fingerprintjs/fingerprintjs';
import sha1 from 'sha1';
import { LocalStorageService } from './local-storage.service';

@Injectable({
  providedIn: 'root',
})
export class FingerprintService {
  private storageKey = 'fingerprint';

  get fingerprint(): string {
    return this.localStorageService.getItem(this.storageKey) || '';
  }

  set fingerprint(value: string) {
    this.localStorageService.setItem(this.storageKey, value);
  }

  constructor(
    @Inject(PLATFORM_ID) private platformId: string,
    private localStorageService: LocalStorageService
  ) {
    if (isPlatformServer(this.platformId)) {
      return;
    }

    if (this.fingerprint) {
      return;
    } else {
      const timestamp = Date.now();

      FingerprintJS.load()
        .then(res => res.get())
        .then(
          result => (this.fingerprint = sha1(result.visitorId + timestamp))
        );
    }
  }
}
