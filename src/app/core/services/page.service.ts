import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map, share } from 'rxjs/operators';
import { BreadCrumb, BreadCrumbResponse, Page, PageResponse } from '../models';

@Injectable({
  providedIn: 'root',
})
export class PageService {
  constructor(private http: HttpClient) {}

  getPagePreview(slug: string): Observable<Page> {
    return this.http.get<PageResponse>(`/pages/preview/?path=${slug}`).pipe(
      map(res => new Page(res)),
      share()
    );
  }

  getPageBody(id: number) {
    return this.http.get<string>(`/pages/${id}/body`, {
      responseType: 'text' as 'json',
    });
  }

  getPageBreadcrumbs(id: number) {
    return this.http
      .get<BreadCrumbResponse[]>(`/pages/${id}/breadcrumbs`)
      .pipe(map(res => res.map(item => new BreadCrumb(item))));
  }

  pageShow(id: number) {
    return this.http.get<PageResponse>(`/pages/${id}`);
  }
}
