import { HashtagResponse } from './hashtags';

export interface MenuResponse {
  id: number;
  parent_id: number;
  external_link: string;
  title: string;
  image: string;
  color: string;
  is_animated: boolean;
  is_banner_menu: boolean;
  description: string;
  hash_tags: HashtagResponse[];
  category_id: number;
  has_children: boolean;
  show_page_preview: boolean;
  path: string;
  created_at: string;
  children?: MenuResponse[];
}

export class Menu implements MenuResponse {
  id!: number;
  parent_id!: number;
  external_link!: string;
  title!: string;
  image!: string;
  color!: string;
  is_animated!: boolean;
  is_banner_menu!: boolean;
  description!: string;
  hash_tags!: HashtagResponse[];
  category_id!: number;
  has_children!: boolean;
  show_page_preview!: boolean;
  path!: string;
  created_at!: string;
  children?: Menu[];

  constructor(data: MenuResponse) {
    Object.assign(this, data);

    if (data.children) {
      this.children = data.children.map(child => new <PERSON>u(child));
    }
  }
}
