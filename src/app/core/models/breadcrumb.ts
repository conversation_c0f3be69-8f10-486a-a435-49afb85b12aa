export interface BreadCrumbResponse {
  id: number;
  title: string;
  path: string;
  contentable_type: string;
  contentable_id: string;
  preview_image: string;
  meta: any;
}

export class BreadCrumb implements BreadCrumbResponse {
  id!: number;
  title!: string;
  path!: string;
  contentable_type!: string;
  contentable_id!: string;
  preview_image!: string;
  meta!: any;

  constructor(data: BreadCrumbResponse) {
    Object.assign(this, data);
  }
}
