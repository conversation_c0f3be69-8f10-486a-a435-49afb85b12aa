import { HashtagResponse } from './hashtags';

export interface Body {
  html: string;
}

export interface SocialMediaSharingLinks {
  vk: string;
  ok: string;
  fb: string;
}

export interface PageResponse {
  id: number;
  path: string;
  contentable_type: string | null;
  contentable_id: number | null;
  meta: Meta[];
  preview_image: string;
  mobile_preview_image: string;
  title: string;
  banner_text_color: string;
  banner_bg_color: string;
  banner_title: string;
  subtitle: string;
  content_for_gamma: string;
  hash_tags: HashtagResponse[];
  body?: Body;
}
export class Page implements PageResponse {
  id!: number;
  path!: string;
  contentable_type!: string | null;
  contentable_id!: number | null;
  meta!: Meta[];
  preview_image!: string;
  mobile_preview_image!: string;
  title!: string;
  banner_text_color!: string;
  banner_bg_color!: string;
  banner_title!: string;
  subtitle!: string;
  content_for_gamma!: string;
  hash_tags!: HashtagResponse[];
  body?: Body;

  constructor(data: PageResponse) {
    Object.assign(this, data);
  }
}

export interface Meta {
  created_at: string;
  id: number;
  key: string;
  language: string;
  page_id: number;
  type: string;
  updated_at: string;
  value: string;
}
