import { Renderer2 } from '@angular/core';

export const insertJivoSiteScript = (
  renderer2: Renderer2,
  documentInjector: Document
) => {
  const srcScript = renderer2.createElement('script');
  srcScript.type = 'text/javascript';
  srcScript.text = `
     (function () {
        var widget_id = 'e5ZKOeDq71'; var d = document; var w = window; function l() {
          var s = document.createElement('script'); s.type = 'text/javascript'; s.async = true; s.src = '//code.jivosite.com/script/widget/' + widget_id; var ss = document.getElementsByTagName('script')[0]; ss.parentNode.insertBefore(s, ss);
        } if (d.readyState == 'complete') { l(); } else { if (w.attachEvent) { w.attachEvent('onload', l); } else { w.addEventListener('load', l, false); } }
      })();
    `;
  renderer2.appendChild(documentInjector.body, srcScript);
};
