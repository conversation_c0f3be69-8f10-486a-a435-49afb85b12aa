import { HttpErrorResponse } from '@angular/common/http';
import { ToastrService } from 'ngx-toastr';

export function showStatusErrors(
  error: HttpErrorResponse,
  toastr: ToastrService,
  errorStatus: number
) {
  if (error.status === errorStatus) {
    for (const value of Object.values(error.error.errors)) {
      if (Array.isArray(value)) {
        value.forEach(errorMsg => toastr.error(errorMsg));
      } else {
        toastr.error(value as string);
      }
    }
  }
}
