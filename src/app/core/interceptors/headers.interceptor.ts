import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>p<PERSON><PERSON><PERSON>,
  HttpInterceptor,
  HttpRequest,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { FingerprintService } from 'Core/services/fingerprint.service';
import { CookieService } from 'ngx-cookie';
import { Observable } from 'rxjs';

@Injectable()
export class HeadersInterceptor implements HttpInterceptor {
  cookies!: string[];

  constructor(
    private fingerprintService: FingerprintService,
    private translateService: TranslateService,
    private cookiesService: CookieService
  ) {}

  intercept(
    req: HttpRequest<any>,
    next: HttpHandler
  ): Observable<HttpEvent<any>> {
    let headers = req.headers;

    headers = headers.set('Accept', 'application/json');
    headers = headers.set('X-Session', this.fingerprintService.fingerprint);
    headers = headers.set('Access-Control-Max-Age', '7200');

    if (this.translateService.currentLang) {
      headers = headers.set('hl', this.translateService.currentLang);
    }

    if (this.cookiesService.getAll() !== null) {
      for (const [key, value] of Object.entries(this.cookiesService.getAll())) {
        headers = headers.append('cookies', `${key}: ${value}`);
      }
    }

    const cloneReq = req.clone({ headers });

    return next.handle(cloneReq);
  }
}
