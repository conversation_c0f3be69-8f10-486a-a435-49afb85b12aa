import { DOCUMENT, isPlatformServer } from '@angular/common';
import {
  AfterViewInit,
  Component,
  Inject,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { Router } from '@angular/router';
import { noIndexNoFollow } from 'Core/scripts';

declare global {
  interface Window {
    bootstrap?: any;
  }
}

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, AfterViewInit {
  get isServer() {
    return isPlatformServer(this.platformId);
  }

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: string,
    private router: Router
  ) {}

  ngOnInit(): void {
    noIndexNoFollow(this.document);
  }

  ngAfterViewInit(): void {
    if (this.isServer) {
      noIndexNoFollow(this.document);
    }
  }
}
