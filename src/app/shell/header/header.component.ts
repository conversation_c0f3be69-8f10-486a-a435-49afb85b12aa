import { isPlatformBrowser } from '@angular/common';
import { Component, Inject, PLATFORM_ID } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { WINDOW } from 'Core/services';
import { CookieService } from 'ngx-cookie';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent {
  activeLng!: string;

  get url() {
    return this.router.url;
  }

  get isBrowser() {
    return isPlatformBrowser(this.platformId);
  }

  constructor(
    @Inject(WINDOW) private window: Window,
    @Inject(PLATFORM_ID) private platformId: string,
    private router: Router,
    private translateService: TranslateService,
    private cookieService: CookieService
  ) {
    this.activeLng = this.translateService.currentLang;
  }

  changeLanguage(lng: string) {
    if (lng !== this.translateService.currentLang) {
      this.translateService.use(lng);
      this.activeLng = lng;
      this.cookieService.put('currentLanguage', lng, { path: '/' });

      if (this.isBrowser) {
        this.window.location.reload();
      }
    }
  }
}
