@use "core" as *;

:host {
  position: sticky;
  top: 0;
  z-index: 50;
}

.header {
  background-color: $green;

  &.hide {
    margin-bottom: 6rem;
  }

  .container {
    display: flex;
    align-items: center;
    height: 9.5rem;
    min-height: 6rem;

    @include to(xl) {
      padding: 0 2rem;
    }

    @include to(s) {
      height: 6.6rem;
    }

    .logo-image {
      width: 16.4rem;
      margin-right: 18.6%;

      @include to(s) {
        margin-right: 2.5rem;
      }

      @include to(s) {
        width: 4.4rem;
      }
    }

    .search {
      position: relative;
      margin: 0 1rem;
      padding: 1rem 0;
      padding-right: 3.8rem;
      color: $white;
      border-bottom: 0.1rem solid $white;

      @include to(l) {
        display: none;
      }

      .search-btn {
        width: 3.5rem;
        height: 1.5rem;
        background-image: url("/assets/img/search-icon.svg");
        background-repeat: no-repeat;
        background-position: 50% 50%;
        background-size: 3.5rem 1.8rem;
        cursor: pointer;
      }

      .clear-btn {
        position: absolute;
        top: 5%;
        right: 0.5rem;
        width: 15px;
        height: 15px;
        cursor: pointer;
        opacity: 0.3;
        transition: opacity 0.2s;

        &:hover {
          opacity: 1;
        }

        &::before,
        &::after {
          content: "";
          position: absolute;
          left: 0;
          width: 2px;
          height: 20px;
          background-color: #fff;
        }

        &::before {
          transform: rotate(45deg);
        }

        &::after {
          transform: rotate(-45deg);
        }
      }

      input {
        width: 25rem;
        color: $white;
        outline: 0;

        @include placeholder {
          color: $white;
          text-transform: lowercase;
        }

        &:focused {
          outline: 0;
        }
      }
    }

    .mob-search {
      .mob-search-row {
        position: fixed;
        top: 0.3rem;
        left: 0;
        z-index: 20;
        display: flex;
        justify-content: center;
        width: 100%;
        padding: 0 2rem;
        background-color: #fff;

        button:first-of-type {
          width: 3rem;
          height: auto;
          padding: 1rem 0 1rem 3rem;
          background-image: url("/assets/img/mob-search-close-btn.svg");
          background-repeat: no-repeat;
          background-position: 50% 50%;
          background-size: auto 2rem;
        }

        button:last-of-type {
          width: 2.4rem;
          height: auto;
          padding: 1rem 1rem 1rem 3rem;
          background-image: url("/assets/img/mob-search-btn.svg");
          background-repeat: no-repeat;
          background-position: 50% 50%;
          background-size: auto 1.8rem;
        }

        input {
          position: relative;
          width: 100%;
          height: 6.6rem;
          outline: 0;
        }
      }

      .mob-search-overlay {
        position: fixed;
        top: 6.9rem;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 10;
        width: 100vw;
        height: 100vh;
        background-color: hsla(0, 0%, 39.2%, 0.6);
      }
    }

    .mob-search-overlay {
      position: fixed;
      top: 6.9rem;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 10;
      width: 100vw;
      height: 100vh;
      background-color: hsla(0, 0%, 39.2%, 0.6);
    }

    .mob-search-menu {
      display: none;
    }

    .mob-menu-canvas-banners {
      position: fixed;
      top: 6.6rem;
      left: 0;
      z-index: 7;
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      padding-bottom: 20rem;
      overflow-y: scroll;
      background-color: $white;
      transform: translateX(100%);
      opacity: 0;
      transition: transform 0.5s, opacity 0.5s;

      &.move-canvas {
        transform: translateX(0%);
        opacity: 1;
      }

      &::-webkit-scrollbar {
        width: 0.5rem;
        background-color: #e0e0e0;
      }

      &::-webkit-scrollbar-thumb {
        background: $green;
      }

      &::-webkit-scrollbar-track {
        background-color: #e0e0e0;
      }
    }
  }
}

.icons {
  display: flex;
  flex: 1;

  .icons-list {
    display: flex;
    align-items: center;
    margin-left: auto;

    .icon {
      flex-shrink: 0;
      cursor: pointer;

      &:not(:first-child) {
        margin-left: 3.2rem;

        @include to(xl) {
          margin-left: 2rem;
        }
      }

      img {
        height: 3.4rem;
      }

      .product-quantity {
        position: absolute;
        top: -0.5rem;
        right: -1.4rem;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 2rem;
        height: 2rem;
        font-size: 1.3rem;
        font-weight: bold;
        text-align: center;
        color: $pink;
        background-color: #fff;
        border-radius: 50%;
        font-variant-numeric: tabular-nums;
      }

      &.language {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 4.9rem;
        height: 4.4rem;
        font-size: 1.4rem;
        line-height: 1.43;
        font-weight: 500;
        text-align: center;
        color: #fff;
        text-transform: uppercase;
        background-color: transparent;
        border-radius: 0.8rem;
        transition: background 0.3s;

        &:not(:first-child) {
          margin-left: 1rem;
        }

        &:hover {
          background-color: #7c8b18;
        }

        &.active {
          color: #fff;
          background-color: #93a137;
        }
      }

      &.login,
      &.from-server {
        display: block;
      }

      &.basket {
        position: relative;
      }

      &.login {
        @include to(l) {
          display: none;
        }
      }

      &.mob-burger {
        display: none;

        @include to(l) {
          display: block;
        }
      }

      &.mob-search-btn {
        display: none;

        @include to(l) {
          display: block;
        }

        button {
          width: 3.4rem;
          height: 3.4rem;
          background-image: url("/assets/img/search-icon.svg");
          background-repeat: no-repeat;
          background-position: 50% 50%;
          background-size: 3.4rem 3.4rem;
        }
      }
    }
  }
}

.mob-search:focus-within {
  .mob-search-menu {
    position: fixed;
    top: 6.9rem;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 3;
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100vh;
    padding: 0 2rem;
    background-color: $white;

    .mob-search-menu-list {
      display: flex;

      li {
        margin-right: 1.2rem;
        margin-bottom: 1rem;
        padding: 0.5rem 1rem;
        background-color: #f7f7f7;
        border-radius: 10rem;

        a {
          font-size: 1.2rem;
          line-height: 1.2;
          font-weight: 300;
          vertical-align: middle;
          text-transform: lowercase;
        }
      }
    }

    dt {
      padding: 1.8rem 0 1rem;
      font-size: 1.7rem;
      font-weight: 500;
      text-transform: uppercase;
    }

    dd {
      li {
        padding: 1rem 0;

        a {
          font-size: 1.6rem;
          font-weight: 300;
        }
      }
    }

    .category-list {
      li {
        a {
          text-transform: capitalize;
        }
      }
    }

    .often-looking-for-list {
      li {
        a {
          text-transform: lowercase;
        }
      }
    }
  }
}

.mob-menu {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 5;
  display: flex;
  flex-direction: column;
  background-color: $white;

  header {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 6.6rem;
    padding: 0 2rem;
    background-color: $green;

    .mob-menu-logo {
      width: 4.4rem;
      height: 4.4rem;

      img {
        height: 100%;
        object-fit: cover;
      }
    }

    button {
      position: relative;
      width: 3.2rem;
      height: 3.2rem;
      margin-left: auto;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 3.2rem;
        height: 3.2rem;
        border-top: 2px solid $white;
        transform: rotate(45deg) translateY(50%) translateX(-1px);
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 3.2rem;
        height: 3.2rem;
        border-bottom: 0.2rem solid $white;
        transform: rotate(-45deg) translateY(-50%);
      }
    }
  }

  .authorization {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    align-self: center;
    width: 89.33%;
    min-height: 7.3rem;
    margin: 1.2rem 0;
    padding: 1.2rem 1.6rem;
    background-color: #f3f4e8;
    border-radius: 0.8rem;

    .heading {
      margin: 0 0 0.2rem;
      font-weight: bold;
    }

    .greetings-text {
      margin: 0 0 0.5rem;
      font-size: 1.2rem;
    }

    button {
      font-size: 1.2rem;
      font-weight: 300;
      text-transform: uppercase;
      text-decoration: underline;
    }
  }

  .categories {
    padding: 0 2rem;

    li {
      padding: 1.6rem 0.4rem;
      font-size: 1.4rem;
      text-transform: uppercase;

      &:not(:last-child) {
        border-bottom: 0.1rem solid rgba(45, 45, 45, 0.5);
      }
    }
  }
}

.mob-menu-canvas {
  position: fixed;
  top: 6.6rem;
  left: 0;
  z-index: 6;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100% - 6.6rem);
  overflow-y: scroll;
  background-color: $white;
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.5s, opacity 0.5s;

  &.move-canvas {
    transform: translateX(0%);
    opacity: 1;
  }

  .canvas-header {
    display: flex;
    flex-shrink: 0;
    align-items: flex-start;
    width: 100%;
    padding: 1.8rem 1.5rem 1.6rem;
    border-bottom: 0.1rem solid #ccc;

    button {
      position: relative;
      width: 1rem;
      height: 1.6rem;
      margin-top: 0.4rem;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        width: 1rem;
        height: 1.2rem;
        border-top: 0.2rem solid #000;
        transform: rotate(-45deg);
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        width: 1rem;
        height: 1.2rem;
        border-bottom: 0.2rem solid #000;
        transform: rotate(45deg);
      }
    }

    .category-title-and-link {
      display: flex;
      flex-direction: column;
      margin-left: 1rem;

      p {
        margin-bottom: 1.6rem;
        font-size: 2rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      a {
        font-size: 1.2rem;
        text-transform: uppercase;
        text-decoration: underline;
      }
    }
  }

  .active-tab {
    font-weight: bold;
  }

  .special-offers {
    display: flex;
    flex-shrink: 0;
    padding: 2.6rem 1.8rem 1rem;

    .special-offer-banner {
      position: relative;
      width: 48%;
      margin-right: 0.5rem;

      .img-wrapper {
        display: flex;
        justify-content: center;
        width: 100%;
        height: 100%;

        img {
          width: 100%;
          height: 20rem;
          border-radius: 0.8rem;
          object-fit: cover;

          @include to(s) {
            height: 16rem;
          }

          @include to(xs) {
            height: 14rem;
          }
        }
      }

      p {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        padding: 0.7rem 1.5rem 1rem;
        font-size: 1.3rem;
        font-weight: bold;
        color: $white;
        background-color: rgba($color: #000, $alpha: 0.4);
        border-bottom-right-radius: 0.8rem;
        border-bottom-left-radius: 0.8rem;
      }
    }

    .special-offer-list {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: center;
      padding: 1.5rem 1.7rem;
      background-color: #f4f4f4;
      border-radius: 0.8rem;

      li {
        padding: 1.5rem 0;

        a {
          font-size: 1.3rem;
          text-transform: uppercase;
          text-decoration: underline;
        }
      }
    }
  }
}

.mob-menu-canvas-banners {
  position: fixed;
  top: 6.6rem;
  left: 0;
  z-index: 7;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100% - 6.6rem);
  padding-bottom: 20rem;
  overflow-y: scroll;
  background-color: $white;
  transform: translateX(100%);
  opacity: 0;
  transition: transform 0.5s, opacity 0.5s;

  &.move-canvas {
    transform: translateX(0%);
    opacity: 1;
  }

  .canvas-header {
    display: flex;
    align-items: flex-start;
    width: 100%;
    padding: 1.8rem 1.5rem 1.6rem;
    border-bottom: 1px solid #ccc;

    button {
      position: relative;
      width: 1rem;
      height: 1.6rem;
      margin-top: 0.4rem;

      &::before {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        width: 1rem;
        height: 1.2rem;
        border-top: 2px solid #000;
        transform: rotate(-45deg);
      }

      &::after {
        content: "";
        position: absolute;
        top: 0;
        bottom: 0;
        width: 1rem;
        height: 1.2rem;
        border-bottom: 2px solid #000;
        transform: rotate(45deg);
      }
    }

    .category-title-and-link {
      display: flex;
      flex-direction: column;
      margin-left: 1rem;

      p {
        font-size: 2rem;
        font-weight: 600;
        text-transform: uppercase;
      }

      a {
        font-size: 1.2rem;
        text-transform: uppercase;
        text-decoration: underline;
      }
    }
  }

  .description-text {
    margin: 2.5rem 5.8rem 3.2rem;
    font-size: 1.2rem;
    text-align: center;
  }

  .images {
    display: flex;
    flex-direction: column;
    align-items: center;
    align-self: center;

    .image-wrapper {
      position: relative;
      width: 76.67%;
      margin-bottom: 0.5rem;

      img {
        width: 100%;
        height: 100%;
        border-radius: 0.8rem;
        object-fit: cover;
      }

      p {
        position: absolute;
        bottom: 0.2rem;
        left: 0;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        width: 100%;
        min-height: 5.9rem;
        padding: 0.6rem 2.8rem;
        font-size: 1.5rem;
        line-height: 1.2;
        color: $white;
        text-transform: capitalize;
        background-color: rgba($color: #000, $alpha: 0.4);
        border-bottom-right-radius: 0.8rem;
        border-bottom-left-radius: 0.8rem;
      }
    }
  }
}

.subcategory-list {
  display: flex;
  flex-direction: column;
  min-height: fit-content;
  padding: 0 2.4rem;

  > li {
    min-height: fit-content;
    padding: 1.9rem 0;
    border-bottom: 1px solid #ccc;

    .subcategory-title-and-button {
      display: flex;
      justify-content: space-between;

      span {
        font-size: 1.7rem;
      }

      div {
        position: relative;
        width: 1.5rem;
        height: 1.5rem;
        cursor: pointer;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 1.5rem;
          height: 1.5rem;
          border-bottom: 1px solid #000;
          transform: translateY(-45%);
        }

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          width: 1.5rem;
          height: 1.5rem;
          border-left: 1px solid #000;
          transform: translateX(45%);
        }
      }

      &.active {
        div::after {
          border: 0;
        }
      }
    }

    > ul {
      display: flex;
      flex-direction: column;

      li {
        padding: 0.4rem 0 2rem;

        &:first-child {
          margin-top: 1.5rem;
        }

        a {
          font-size: 1.3rem;
          text-transform: capitalize;
        }
      }
    }
  }

  .view-all {
    margin-top: 1rem;
    font-size: 1.2rem;
    text-transform: uppercase;
    text-decoration: underline;
  }
}

.search-result {
  position: absolute;
  top: 5rem;
  left: -25.35%;
  z-index: 30;
  display: flex;
  flex-direction: column;
  width: 48rem;
  background-color: #fff;
  box-shadow: 0 1rem 1rem 0 rgba(0, 0, 0, 0.13);

  @include to(l) {
    position: fixed;
    top: 6.6rem;
    right: 0;
    bottom: 0;
    left: 0;
    width: 100vw;
  }

  &::before {
    content: "";
    position: absolute;
    top: -0.63rem;
    right: 0;
    left: 0;
    width: 0.63rem;
    margin-right: auto;
    margin-left: auto;
    border-width: 0 0.63rem 0.63rem;
    border-style: solid;
    border-color: #fff transparent;
  }

  .search-suggest {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: hidden;

    .maybe-you-search {
      padding: 1.2rem;
      background-color: #f3f4e8;

      div {
        font-size: 1.4rem;
        color: #000;
      }
    }

    .suggestion-list {
      display: flex;
      box-sizing: content-box;
      width: 100%;
      height: 100%;
      padding: 1.2rem;
      padding-bottom: 17px;
      overflow-x: scroll;

      .suggestion-item {
        flex: 1;
        margin: 0.5rem;
        padding: 0.5rem;
        color: #000;
        word-break: keep-all;
        border: 0.1rem solid #000;
        border-radius: 1.25rem;
        cursor: pointer;
      }
    }
  }

  .heading-and-link {
    position: relative;
    display: flex;
    align-items: center;
    padding: 0 1.2rem;
    background-color: #f3f4e8;

    div {
      margin: 2rem 1rem 2rem 0;
      font-size: 1.4rem;
      color: #000;
    }

    a {
      margin-left: auto;
      font-size: 1.2rem;
      color: #000;
      text-transform: uppercase;
      text-decoration: underline;
    }
  }

  .search-result-list {
    display: flex;
    flex-direction: column;
    max-height: 39.6rem;
    overflow-y: auto;

    @include to(l) {
      max-height: 100%;
    }

    .result-item {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      height: 12rem;
      margin-top: 1.2rem;
      padding-bottom: 1.2rem;
      cursor: pointer;

      &:not(:last-child) {
        border-bottom: 0.1rem solid #e6e6e6;
      }

      .img-wrapper {
        display: flex;
        justify-content: center;
        align-items: center;
        max-width: 8rem;
        margin-right: 1rem;
        margin-left: 2rem;
      }

      .title-and-shape {
        h4 {
          margin-bottom: 0.5rem;
          font-size: 1.5rem;
          color: #000;
        }

        p {
          margin-bottom: 1.2rem;
          font-size: 1.3rem;
          font-weight: 300;
          color: #000;
        }
      }
    }
  }
}

.visually-impared {
  display: flex;
  align-self: center;
}

.categories-list {
  display: flex;
  flex-direction: column;
  padding: 2rem;

  .categories-list-item {
    flex: 1;
    padding: 1.6rem 2rem;
    line-height: 1.25;
    font-weight: 500;
    color: #232323;
    background-color: transparent;
    border-radius: 0.8rem;
    transition: background 0.3s;

    &:hover {
      background-color: #f5f5f5;
    }

    &.active {
      color: #ba1051;
      background-color: #ffe2ed;
    }

    &:not(:last-child) {
      margin-bottom: 0.5rem;
    }
  }
}
