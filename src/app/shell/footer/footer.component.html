<footer>
  <div class="container middle-footer">
    <div class="left-wrapper">
      <ul class="menu-list">
        <ng-container
          *ngFor="let submenu of menu; let i = index; let last = last"
        >
          <li class="menu-list-item" *ngIf="!last">
            <div
              class="unselectable"
              (click)="
                currentTab === submenu.id
                  ? (currentTab = 0)
                  : (currentTab = submenu.id)
              "
              [class.opened-menu]="currentTab === submenu.id"
            >
              {{ submenu.title }}
            </div>
            <ul class="list" *ngIf="isDesktop || currentTab === submenu.id">
              <li class="list-item" *ngFor="let item of submenu.children">
                <a [href]="'https://www.y-r.by/' + item.path">{{
                  item.title
                }}</a>
              </li>
            </ul>
          </li>
        </ng-container>
      </ul>

      <div class="confidential-policy">
        <ul class="other-data-list">
          <li *ngFor="let item of footerBottomMenu">
            <a
              [routerLink]="['/', item?.path]"
              [appExternalLink]="item?.external_link"
              class="other-data-item"
              >{{ item?.title }}</a
            >
          </li>
        </ul>
      </div>
    </div>
    <div class="right-wrapper">
      <div class="subscribe-to-maillist-and-work-data">
        <div class="maillist">
          <p>
            {{ 'FOOTER.SUBSCRIBE_TO' | translate }}
            <span>{{ 'FOOTER.MAILLIST' | translate }}</span>
          </p>
          <div class="input-wrapper eye-form eye-border">
            <form [formGroup]="footerForm">
              <input
                type="text"
                placeholder="{{ 'FOOTER.EMAIL_PLACEHOLDER' | translate }}"
                formControlName="email"
                (keyup.enter)="subToMailingList()"
                trim
              />
              <button
                type="button"
                aria-label="arrow-btn"
                [class.disabled]="!footerForm?.valid"
                (click)="subToMailingList()"
              ></button>
            </form>
          </div>
        </div>

        <div class="phone eye-bg">
          <div class="phone-number">
            <span>{{ 'FOOTER.CONTACT_CENTER' | translate }}</span
            ><a href="{{ 'FOOTER.NUMBER_HREF' | translate }}">{{
              'FOOTER.NUMBER' | translate
            }}</a>
          </div>
          <ul class="schedule">
            <li>{{ 'FOOTER.SCHEDULE' | translate }}</li>
            <li>{{ 'FOOTER.SCHEDULE_WEEKEND' | translate }}</li>
          </ul>
        </div>
      </div>
      <div class="applications-wrapper">
        <p class="our-apps">{{ 'FOOTER.OUR_APPLICATIONS' | translate }}</p>
        <ul class="apps-list">
          <li class="apps-list-item">
            <a href="{{ 'FOOTER.GOOGLE_APP_LINK' | translate }}">
              <picture>
                <source src="/assets/img/google-play-logo.svg" />
                <img
                  src="/assets/img/google-play-logo.svg"
                  alt="{{ 'FOOTER.GOOGLE_APP' | translate }}"
                />
              </picture>
            </a>
          </li>
          <li class="apps-list-item">
            <a href="{{ 'FOOTER.APPLE_APP_LINK' | translate }}">
              <picture class="apple">
                <source src="/assets/img/app-store-icon.svg" />
                <img
                  src="/assets/img/app-store-icon.svg"
                  alt="{{ 'FOOTER.APPLE_APP' | translate }}"
                />
              </picture>
            </a>
          </li>
        </ul>
      </div>
      <ul class="payments-list eye-none">
        <li class="payments-item">
          <img
            class="visa-logo"
            src="/assets/img/payment-logos/visa-logo.svg"
            alt="Visa"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
        <li class="payments-item">
          <img
            class="belkart_internetparol-logo"
            src="/assets/img/payment-logos/belkart_internetparol-logo.svg"
            alt="Belkart International"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
        <li class="payments-item">
          <img
            class="alpha-bank-logo"
            src="/assets/img/payment-logos/alpha-bank-logo.svg"
            alt="Samsung Pay"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
        <li class="payments-item">
          <img
            class="belkart-logo"
            src="/assets/img/payment-logos/belkart-logo.svg"
            alt="Belkart"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
        <li class="payments-item">
          <img
            class="mc_logo"
            src="/assets/img/payment-logos/mc_logo.svg"
            alt="Master Card"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
        <li class="payments-item">
          <img
            class="samsung-pay-logo"
            src="/assets/img/payment-logos/samsung-pay-logo.svg"
            alt="Samsung Pay"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
        <li class="payments-item">
          <img
            class="mc_idcheck_logo"
            src="/assets/img/payment-logos/mc_idcheck_logo.svg"
            alt="Master Card ID Check"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
        <li class="payments-item">
          <img
            class="apple-pay-logo"
            src="/assets/img/payment-logos/apple-pay-logo.svg"
            alt="Apple Pay"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
        <li class="payments-item">
          <img
            class="visa-secure-logo"
            src="/assets/img/payment-logos/visa-secure-logo.svg"
            alt="Visa Secure"
            loading="lazy"
            width="184"
            height="69"
          />
        </li>
      </ul>
    </div>
  </div>
  <div class="bottom-footer eye-bg">
    <div class="container">
      <div class="social-media-wrapper">
        <p class="social-media-title">
          {{ 'FOOTER.SOCIAL_MEDIA_TITLE' | translate }}
        </p>
        <ul class="social-bar">
          <a
            href="{{ 'FOOTER.INSTAGRAM_LINK' | translate }}"
            target="_blank"
            aria-label="Instagram"
            rel="nofollow"
          >
            <li class="social-bar-item instagram"></li>
          </a>
          <a
            href="{{ 'FOOTER.YOUTUBE_LINK' | translate }}"
            target="_blank"
            aria-label="Youtube"
            rel="nofollow"
          >
            <li class="social-bar-item youtube"></li>
          </a>
          <a
            href="{{ 'FOOTER.VK_LINK' | translate }}"
            target="_blank"
            aria-label="VK"
            rel="nofollow"
          >
            <li class="social-bar-item vk"></li>
          </a>
          <a
            href="{{ 'FOOTER.FACEBOOK_LINK' | translate }}"
            target="_blank"
            aria-label="Facebook"
            rel="nofollow"
          >
            <li class="social-bar-item facebook"></li>
          </a>
          <a
            href="{{ 'FOOTER.OK_LINK' | translate }}"
            target="_blank"
            aria-label="OK"
            rel="nofollow"
          >
            <li class="social-bar-item ok"></li>
          </a>
        </ul>
      </div>
    </div>
  </div>
  <div class="unp-info-container eye-bg">
    <div class="container">
      <div
        class="unp-wrapper"
        itemscope
        itemtype="https://schema.org/Organization"
      >
        <p class="unp-info">© {{ 'FOOTER.COMPANY.TITLE' | translate }}</p>
        <p class="unp-info">
          <span itemprop="name">{{ 'FOOTER.COMPANY.NAME' | translate }}</span>
        </p>
        <p
          class="unp-info"
          itemprop="address"
          itemscope
          itemtype="https://schema.org/PostalAddress"
        >
          {{ 'FOOTER.COMPANY.ADDRESS_TITLE' | translate }}
          <span itemprop="postalCode">{{
            'FOOTER.COMPANY.POSTAL_CODE' | translate
          }}</span
          >, <meta itemprop="addressCountry" content="BY" />
          <span>
            {{ 'FOOTER.COMPANY.COUNTRY' | translate }} </span
          >,
          <span itemprop="addressLocality">
            {{ 'FOOTER.COMPANY.CITY' | translate }} </span
          >,
          <span itemprop="streetAddress">
            {{ 'FOOTER.COMPANY.ADDRESS' | translate }}
          </span>
        </p>
        <p class="unp-info">
          {{ 'FOOTER.COMPANY.STATE_REGISTRATION_INFO_1' | translate }}
        </p>
        <p class="unp-info">
          {{ 'FOOTER.COMPANY.STATE_REGISTRATION_INFO_2' | translate }}
        </p>
        <p class="unp-info">
          {{ 'FOOTER.COMPANY.DEPARTMENT_OF_TRADE' | translate }}
          <a
            [href]="
              'tel:' +
              ('FOOTER.COMPANY.DEPARTMENT_OF_TRADE_PHONE_1' | translate)
            "
          >
            {{ 'FOOTER.COMPANY.DEPARTMENT_OF_TRADE_PHONE_1' | translate }}</a
          >,
          <a
            [href]="
              'tel:' +
              ('FOOTER.COMPANY.DEPARTMENT_OF_TRADE_PHONE_2' | translate)
            "
          >
            {{ 'FOOTER.COMPANY.DEPARTMENT_OF_TRADE_PHONE_2' | translate }}</a
          >
        </p>
        <p class="unp-info">
          {{ 'FOOTER.COMPANY.CUSTOMER_REQUESTS' | translate }}
          <a
            itemprop="email"
            [href]="
              'mailto:' + ('FOOTER.COMPANY.CUSTOMER_REQUESTS_EMAIL' | translate)
            "
          >
            {{ 'FOOTER.COMPANY.CUSTOMER_REQUESTS_EMAIL' | translate }}</a
          >,
          <a
            itemprop="telephone"
            [href]="
              'tel:' + ('FOOTER.COMPANY.CUSTOMER_REQUESTS_PHONE' | translate)
            "
          >
            {{ 'FOOTER.COMPANY.CUSTOMER_REQUESTS_PHONE' | translate }}</a
          >
        </p>
      </div>

      <a
        class="img-wrapper desktop"
        href="{{ 'FOOTER.COMPANY.DEVELOPMENT_LINK' | translate }}"
      >
        <p class="desc">{{ 'FOOTER.COMPANY.DEVELOPMENT' | translate }}</p>
        <img src="/assets/img/section228-logo.svg" alt="" />
      </a>
    </div>
  </div>
</footer>
