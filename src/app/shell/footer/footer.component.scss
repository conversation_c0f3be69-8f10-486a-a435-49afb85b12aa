@use "core" as *;

footer {
  display: flex;
  flex-direction: column;

  .top-footer {
    padding-top: 3rem;

    @include to(xl) {
      padding: 3rem 2rem;
    }

    @include to(l) {
      padding: 0;
    }

    .container {
      display: flex;
      justify-content: space-between;

      @include to(l) {
        flex-direction: column;
      }
    }
  }
}

.basket-footer {
  display: inherit;
}

.applications-wrapper-basket {
  display: flex;
  flex-direction: column;
  width: 25%;
  margin-bottom: 1rem;

  @include to(l) {
    width: 50%;
  }

  @include to(s) {
    width: 90%;
  }

  .our-apps {
    padding-top: 1rem;
    font-size: 1.6rem;
    font-weight: bold;
    text-align: center;
    text-transform: uppercase;
  }

  .apps-list {
    display: flex;
    margin-top: 1rem;

    .apps-list-item {
      display: flex;
      flex: 0 0 50%;
      justify-content: flex-end;
      align-items: center;

      a {
        display: flex;
        flex: 0 0 100%;
        justify-content: flex-end;
        align-items: center;
      }

      picture.apple {
        width: 90%;
      }

      img {
        width: 100%;
      }
    }
  }
}

.subscribe-to-maillist {
  margin-bottom: 2rem;
  background-color: #60472e;

  @include to(l) {
    flex-direction: column;
  }
}

.container.middle-footer {
  display: flex;
  margin-top: 6rem;

  @include to(l) {
    flex-direction: column;
    margin-top: 3.2rem;
  }

  .left-wrapper,
  .right-wrapper {
    flex: 0 0 50%;

    @include to(l) {
      flex: auto;
    }
  }

  .left-wrapper {
    display: flex;
    flex-direction: column;

    .menu-list {
      display: flex;
      flex-wrap: wrap;

      @include to(l) {
        flex-direction: column;
        padding: 0 2rem;
      }
    }

    .other-data-list {
      display: flex;
      flex-wrap: wrap;
      justify-self: flex-start;
      margin-top: 11.2rem;

      @include to(l) {
        flex-wrap: wrap;
        align-items: center;
        margin-top: 0.6rem;
        margin-bottom: 1rem;
        padding: 0 2rem;
      }

      @include to(s) {
        margin-bottom: 3.6rem;
      }

      .other-data-item {
        margin-right: 2rem;
        font-size: 1.4rem;
        line-height: 1.23;

        @include to(l) {
          line-height: 1.6;
          text-decoration: underline;
        }

        @include to(xl) {
          margin-right: 0.7rem;
        }
      }
    }
  }

  .right-wrapper {
    display: flex;
    flex-direction: column;

    .subscribe-to-maillist-and-work-data {
      display: flex;
      flex-direction: column;
      padding: 2.8rem 2.4rem;
      background-color: #f8f8f8;
      border-radius: 0.4rem;

      @include to(l) {
        order: 0;
        padding: 2.4rem 2rem;
      }
    }
  }
}

.payments-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;

  @include to(l) {
    order: 1;
    margin: 0;
    padding: 0;
  }

  .payments-item {
    display: flex;
    flex: 0 0 31.19%;
    justify-content: center;
    align-items: center;
    height: 6.9rem;
    margin-bottom: 2rem;
    text-align: center;
    border: 0.1rem solid #f2f2f2;
    border-radius: 0.4rem;

    @include to(l) {
      display: flex;
      flex: 0 0 33.33%;
      justify-content: center;
      align-items: center;
      margin-bottom: 0;
      border-width: 0.05rem;
      border-radius: 0;
    }

    img.visa-logo {
      width: 29.92%;

      @include to(l) {
        width: 48.63%;
      }
    }

    img.belkart_internetparol-logo {
      width: 66.3%;

      @include to(l) {
        width: 80.83%;
      }
    }

    img.alpha-bank-logo {
      width: 34.24%;

      @include to(l) {
        width: 52.5%;
      }
    }

    img.belkart-logo {
      width: 22.28%;

      @include to(l) {
        width: 34.17%;
      }
    }

    img.mc_logo {
      width: 28.8%;

      @include to(l) {
        width: 44.17%;
      }
    }

    img.samsung-pay-logo {
      width: 39.13%;

      @include to(l) {
        width: 60%;
      }
    }

    img.mc_idcheck_logo {
      width: 26.09%;

      @include to(l) {
        width: 40%;
      }
    }

    img.apple-pay-logo {
      width: 33.15%;

      @include to(l) {
        width: 50.83%;
      }
    }

    img.visa-secure-logo {
      width: 27.17%;

      @include to(l) {
        width: 41.67%;
      }
    }
  }
}

.maillist {
  display: flex;
  flex-direction: column;
  padding-bottom: 2.8rem;
  border-bottom: 0.1rem solid #e7e7e7;

  @include to(l) {
    flex-direction: column;
  }

  p {
    font-size: 1.4rem;
    line-height: 1.23;
    font-weight: 600;
    text-transform: uppercase;
  }

  .input-wrapper {
    flex: 0 0 100%;
    margin-top: 2rem;

    form {
      display: flex;
      flex: 0 0 100%;
      justify-content: space-between;
    }

    input {
      flex: 0 0 88%;
      height: 5.2rem;
      padding: 0 2rem;
      background-color: $white;
      border: 0.1rem solid #e7e7e7;
      border-radius: 0.8rem;

      @include to(l) {
        flex: 0 0 79%;
      }
    }

    button {
      flex: 0 0 5.2rem;
      width: 5.2rem;
      height: 5.2rem;
      background-image: url("/assets/img/auth-redesign/footer/subscribe-to-maillist-icon.svg");
      background-repeat: no-repeat;
      background-position: center;
      background-size: 5.2rem 5.2rem;
      border-top-right-radius: 0.3rem;
      border-bottom-right-radius: 0.3rem;

      @include to(l) {
        margin-left: 1.2rem;
      }
    }
  }
}

.phone {
  display: flex;
  flex-direction: column;
  margin-top: 2.8rem;

  .phone-number {
    font-size: 1.4rem;
    line-height: 1.23;
    font-weight: 600;
    text-transform: uppercase;

    span {
      margin-right: 0.8rem;
      color: #7c7e89;
    }
  }

  .schedule {
    display: flex;
    margin-top: 2rem;

    @include to(l) {
      flex-direction: column;
    }

    li {
      position: relative;
      padding-left: 1.4rem;
      font-size: 1.4rem;
      line-height: 1.43;
      color: #7c7e89;

      &::before {
        content: "";
        position: absolute;
        top: calc(50% - 0.3rem);
        left: 0;
        width: 0.6rem;
        height: 0.6rem;
        background-color: #7c7e89;
        border-radius: 50%;
      }

      &:not(:last-child) {
        margin-right: 2.4rem;

        @include to(l) {
          margin-right: 0;
          margin-bottom: 1.6rem;
        }
      }
    }
  }
}

.applications-wrapper {
  display: flex;
  align-items: center;
  margin-top: 2rem;
  padding: 2.8rem;
  background-color: #f8f8f8;
  border-radius: 0.4rem;

  @include to(l) {
    flex-direction: column;
    align-items: flex-start;
    order: 2;
    margin-top: 0;
    padding: 2.4rem 2rem;
  }

  .our-apps {
    font-size: 1.4rem;
    line-height: 1.23;
    font-weight: 600;
    text-transform: uppercase;

    @include to(l) {
      margin-bottom: 2rem;
    }
  }

  .apps-list {
    display: flex;
    flex: 0 0 60%;
    justify-content: space-between;
    margin-left: 3.3rem;

    @include to(l) {
      width: 100%;
      margin-left: 0;
    }

    .apps-list-item {
      display: flex;
      flex: 0 0 48.22%;
      justify-content: flex-end;
      align-items: center;

      a {
        display: flex;
        flex: 0 0 100%;
        justify-content: flex-end;
        align-items: center;

        picture {
          width: 100%;
        }
      }

      img {
        width: 100%;
      }
    }
  }
}

.menu-list-item {
  display: flex;
  flex: 0 0 50%;
  flex-direction: column;
  margin-bottom: 6rem;

  @include to(l) {
    position: relative;
    margin-bottom: 3.2rem;
  }

  div {
    margin-bottom: 2.4rem;
    padding-right: 2.5rem;
    font-size: 1.4rem;
    line-height: 1.22;
    font-weight: 600;
    text-transform: uppercase;

    @include to(l) {
      margin-bottom: 0;
      line-height: 1.43;

      &::before {
        content: "";
        position: absolute;
        top: -0.2rem;
        right: 1rem;
        width: 2.4rem;
        height: 2.4rem;
        background-image: url("/assets/img/auth-redesign/footer/plus-icon.svg");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 2.4rem 2.4rem;
      }
    }

    &.opened-menu {
      @include to(l) {
        color: $green;

        &::before {
          background-image: url("/assets/img/auth-redesign/footer/minus-icon.svg");
        }
      }
    }
  }

  .list {
    display: flex;
    flex-direction: column;

    @include to(l) {
      padding-top: 1.8rem;
    }

    .list-item {
      font-size: 1.4rem;
      line-height: 1.22;

      @include to(l) {
        margin-bottom: -0.1rem;

        &:last-child {
          margin-bottom: 0.2rem;
        }

        a {
          margin-left: 2rem;

          @include to(l) {
            margin-left: 0;
          }
        }
      }

      &:not(:last-child) {
        padding-bottom: 2rem;
      }

      a {
        cursor: pointer;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }
}

.bottom-footer {
  margin-top: 6rem;

  @include to(l) {
    margin-top: 0;
  }

  .container {
    display: flex;
    max-width: 124.8rem;
    padding: 1.8rem 0;
    border-top: 0.1rem solid #dedede;
    border-bottom: 0.1rem solid #dedede;

    @include to(l) {
      padding: 2.8rem 2rem;
    }
  }

  .social-media-wrapper {
    display: flex;
    flex: 1;
    align-items: center;

    @include to(s) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  .social-media-title {
    font-size: 1.4rem;
    line-height: 1.43;
    font-weight: 600;
    text-transform: uppercase;

    @include to(s) {
      margin-bottom: 2rem;
    }
  }

  .social-bar {
    display: flex;
    align-items: center;
    margin-left: auto;
    padding-left: 0.5rem;

    @include to(s) {
      justify-content: center;
      width: 100%;
      padding: 0;
    }

    a {
      @include to(s) {
        flex: 0 0 20%;
      }

      &:not(:last-child) {
        margin-right: 3.2rem;

        @include to(s) {
          margin-right: 0;
        }
      }
    }

    .social-bar-item {
      width: 3.6rem;
      height: 3.6rem;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 3.6 3.6rem;

      &.instagram {
        background-image: url("/assets/img/footer-instagram-icon.svg");
      }

      &.youtube {
        background-image: url("/assets/img/footer-youtube-icon.svg");
      }

      &.vk {
        background-image: url("/assets/img/footer-vk-icon.svg");
      }

      &.facebook {
        background-image: url("/assets/img/footer-facebook-icon.svg");
      }

      &.ok {
        background-image: url("/assets/img/footer-ok-icon.svg");
      }
    }
  }
}

.unp-info-container {
  padding: 6rem 0;

  @include to(l) {
    padding: 2.4rem 0;
  }

  .container {
    display: flex;

    @include to(xl) {
      padding: 0 2rem;
    }

    @include to(l) {
      flex-direction: column-reverse;
    }
  }

  .img-wrapper {
    margin-left: auto;

    @include to(l) {
      margin-bottom: 2rem;
      margin-left: 0;
    }

    img {
      display: flex;
      margin-left: auto;

      @include to(l) {
        margin-left: 0;
      }
    }

    .desc {
      margin-bottom: 1rem;
      font-size: 1.2rem;
      line-height: 1.3;
      font-weight: normal;
      color: #7d7d7d;
      font-style: normal;

      @include to(l) {
        margin-bottom: 0.5rem;
      }
    }
  }

  p.unp-info {
    font-size: 1rem;
    line-height: 1.3;
    font-weight: normal;
    color: #7d7d7d;
  }
}
