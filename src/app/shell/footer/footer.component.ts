import { Component, Inject, OnInit } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Menu } from 'Core/models';
import { MenuService, WINDOW } from 'Core/services';

@Component({
  selector: 'app-footer',
  templateUrl: './footer.component.html',
  styleUrls: ['./footer.component.scss'],
})
export class FooterComponent implements OnInit {
  currentTab!: number;
  menu?: Menu[];
  footerBottomMenu!: Menu[];
  year = new Date();
  footerForm!: FormGroup;

  get isDesktop() {
    if (window.innerWidth > 1000) {
      return true;
    }
    return false;
  }

  constructor(
    @Inject(WINDOW) private window: Window,
    private menuService: MenuService,
    private fb: FormBuilder
  ) {}

  ngOnInit(): void {
    this.getMenu();
    this.getFooterBotttomMenu();

    this.footerForm = this.fb.group({
      email: [
        null,
        [
          Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9-]+\\.{1}[a-z]{2,4}$'),
          Validators.maxLength(255),
          Validators.required,
        ],
      ],
    });
  }

  getMenu() {
    this.menuService.getMenu('footer').subscribe(res => {
      this.menu = res;
    });
  }

  getFooterBotttomMenu() {
    this.menuService.getMenu('footer-bottom-menu').subscribe(res => {
      this.footerBottomMenu = res;
    });
  }

  subToMailingList() {
    if (this.footerForm.get('email')?.value && this.footerForm.valid) {
      this.window.location.href = `https://www.y-r.by/newsletter?email=${
        this.footerForm.get('email')?.value
      }`;
    }
  }
}
