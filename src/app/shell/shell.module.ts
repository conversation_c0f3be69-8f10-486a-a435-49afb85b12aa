import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../shared/shared.module';
import { FooterComponent } from './footer/footer.component';
import { HeaderComponent } from './header/header.component';
import { ShellComponent } from './shell.component';

@NgModule({
  declarations: [ShellComponent, FooterComponent, HeaderComponent],
  imports: [CommonModule, SharedModule, RouterModule],
})
export class ShellModule {}
