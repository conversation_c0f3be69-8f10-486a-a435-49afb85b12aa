import {
  HttpClient,
  HttpClientModule,
  HTTP_INTERCEPTORS,
} from '@angular/common/http';
import { APP_INITIALIZER, NgModule } from '@angular/core';
import {
  BrowserModule,
  BrowserTransferStateModule,
} from '@angular/platform-browser';

import { DatePipe } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { UrlSerializer } from '@angular/router';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { CustomUrlSerializer } from 'Core/helpers/custom-url-serializer';
import {
  BackendPrefixInterceptor,
  BrowserStateInterceptor,
  HeadersInterceptor,
} from 'Core/interceptors';
import { WINDOW_PROVIDERS } from 'Core/services';
import { environment } from 'Env';
import { CookieModule, CookieService } from 'ngx-cookie';
import { NgxMasonryModule } from 'ngx-masonry';
import { MetafrenzyModule } from 'ngx-metafrenzy';
import { ToastrModule } from 'ngx-toastr';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { BlogModule } from './pages/blog/blog.module';
import { ShellModule } from './shell/shell.module';

export function httpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, '/translations?language=', '');
}

export function appInitializerFactory(
  translateService: TranslateService,
  cookieService: CookieService
) {
  return () => {
    translateService.addLangs(environment.locales);
    translateService.setDefaultLang(environment.defaultLocale);

    const savedLanguage = cookieService.get('currentLanguage')?.slice(0, 2);
    let currentLanguage = savedLanguage || translateService.getDefaultLang();

    if (!savedLanguage) {
      currentLanguage = translateService.getBrowserLang() || '';
    }

    if (!translateService.getLangs().includes(currentLanguage)) {
      currentLanguage = translateService.getDefaultLang();
    }

    cookieService.put('currentLanguage', currentLanguage);
    translateService.onLangChange.subscribe(e => {
      cookieService.put('currentLanguage', e.lang);
    });

    return translateService.use(currentLanguage);
  };
}

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule.withServerTransition({ appId: 'serverApp' }),
    AppRoutingModule,
    HttpClientModule,
    MetafrenzyModule.forRoot(),
    ShellModule,
    BlogModule,
    BrowserAnimationsModule,
    ReactiveFormsModule,
    BrowserTransferStateModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: httpLoaderFactory,
        deps: [HttpClient],
      },
    }),
    ToastrModule.forRoot({
      maxOpened: 3,
    }),
    CookieModule.forRoot(),
    NgxMasonryModule,
  ],
  providers: [
    DatePipe,
    WINDOW_PROVIDERS,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: BackendPrefixInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HeadersInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: BrowserStateInterceptor,
      multi: true,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService, CookieService],
      multi: true,
    },
    { provide: UrlSerializer, useClass: CustomUrlSerializer },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
