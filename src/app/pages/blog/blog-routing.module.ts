import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BlogCategoriesListComponent } from './blog-categories-list/blog-categories-list.component';
import { BlogPageComponent } from './blog-page/blog-page.component';

import { BlogPagesListComponent } from './blog-pages-list/blog-pages-list.component';
import { BlogComponent } from './blog.component';

const routes: Routes = [
  {
    path: '',
    component: BlogComponent,
    children: [
      {
        path: '',
        component: BlogPagesListComponent,
        pathMatch: 'full',
      },
      {
        path: 'search-by-tag',
        component: BlogPagesListComponent,
        pathMatch: 'full',
      },
      {
        path: 'issues',
        component: BlogCategoriesListComponent,
        pathMatch: 'full',
      },
      {
        path: 'issues/:slug/:slug',
        component: BlogPageComponent,
        pathMatch: 'full',
      },
      {
        path: 'issues/:slug',
        component: BlogPagesListComponent,
        pathMatch: 'full',
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BlogRoutingModule {}
