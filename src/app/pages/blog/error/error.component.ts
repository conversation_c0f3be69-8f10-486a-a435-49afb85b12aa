import { isPlatformServer } from '@angular/common';
import {
  Component,
  Inject,
  Injector,
  OnInit,
  PLATFORM_ID,
} from '@angular/core';
import { RESPONSE } from '@nguniversal/express-engine/tokens';
import { TranslateService } from '@ngx-translate/core';
import { MetafrenzyService } from 'ngx-metafrenzy';

@Component({
  selector: 'app-error',
  templateUrl: './error.component.html',
  styleUrls: ['./error.component.scss'],
})
export class ErrorComponent implements OnInit {
  constructor(
    @Inject(PLATFORM_ID) private platformId: string,
    private injector: Injector,
    private translateService: TranslateService,
    private metaService: MetafrenzyService
  ) {}

  ngOnInit(): void {
    if (isPlatformServer(this.platformId)) {
      const response = this.injector.get(RESPONSE);
      response.status(404);
    }

    this.metaService.setTitle(
      this.translateService.instant('META_TAGS.ERROR_PAGE')
    );
  }
}
