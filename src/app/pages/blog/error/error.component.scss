@use "core" as *;

.container {
  display: flex;
  flex-direction: column;
  padding-bottom: 2rem;

  .breadcrumbs {
    display: flex;
    flex-wrap: wrap;
    padding: 2rem 0.5rem 0.5rem;

    li {
      font-size: 1.3rem;
      color: #2d2d2d;

      &:not(:first-child) {
        margin-left: 0.5rem;

        &::before {
          content: ">";
          margin-right: 0.5rem;
        }
      }
    }
  }

  h1 {
    margin: 1rem 0 1.6rem;
    font-size: 2.2rem;
  }

  .error-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 8rem;
    background-image: url("/assets/img/error_image.jpeg");
    background-repeat: repeat;
    background-size: cover;

    h2 {
      margin-bottom: 1.2rem;
      font-size: 2.5rem;
      color: #fff;
    }

    p {
      margin-bottom: 2rem;
      font-size: 1.4rem;
      text-align: center;
      color: #fff;
    }

    button {
      height: 5rem;
      padding-right: 3rem;
      padding-left: 3rem;
      font-size: 1.4rem;
      font-weight: bold;
      color: #fff;
      text-transform: uppercase;
      white-space: nowrap;
      background-color: $green;
      border-radius: 1rem;
    }
  }
}
