import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { SharedModule } from 'Shared/shared.module';
import { BlogPageComponent } from './blog-page/blog-page.component';
import { BlogCategoriesListComponent } from './blog-categories-list/blog-categories-list.component';
import { BlogRoutingModule } from './blog-routing.module';
import { BlogComponent } from './blog.component';
import { ErrorComponent } from './error/error.component';
import { BlogPagesListComponent } from './blog-pages-list/blog-pages-list.component';

@NgModule({
  declarations: [
    BlogComponent,
    BlogCategoriesListComponent,
    BlogPageComponent,
    ErrorComponent,
    BlogPagesListComponent,
  ],
  imports: [CommonModule, SharedModule, BlogRoutingModule, RouterModule],
})
export class BlogModule {}
