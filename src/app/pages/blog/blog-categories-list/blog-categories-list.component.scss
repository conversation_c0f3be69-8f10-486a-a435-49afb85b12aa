@use "core" as *;

.container {
  display: flex;
  flex-direction: column;
  padding-bottom: 3.1rem;

  @include to(xl) {
    padding: 4rem 1.6rem;
    padding-bottom: 3.1rem;
  }

  @include to(s) {
    padding-top: 2.4rem;
    padding-bottom: 1.6rem;
  }

  .blog-wrapper {
    width: 100%;

    h2 {
      margin-bottom: 2rem;
      font-size: 5rem;
      line-height: 1;
      font-weight: 500;
      text-align: center;

      @include to(s) {
        font-size: 3rem;
      }
    }

    .issues-wrapper {
      display: flex;
      flex-wrap: wrap;
      margin: -1%;

      .issue {
        position: relative;
        flex: 0 0 31.3%;
        margin: 1%;
        overflow: hidden;
        transition: transform 0.5s;
        will-change: transform;

        @include to(l) {
          flex: 0 0 48%;
        }

        &:hover {
          transform: scale(1.03);

          .img-wrapper {
            filter: brightness(0.9);
          }
        }

        .title {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          width: 100%;
          padding-top: 27%;
          padding-bottom: 34.5%;
          font-size: 1.4rem;
          text-align: center;
          color: #fff;

          @include to(s) {
            font-size: 0.9rem;
          }
        }

        .description {
          position: absolute;
          bottom: 12%;
          left: 0;
          z-index: 1;
          width: 100%;
          font-size: 3rem;
          line-height: 1;
          font-weight: 500;
          text-align: center;
          color: #fff;

          @include to(s) {
            font-size: 1.8rem;
          }
        }

        .img-wrapper {
          width: 100%;
          height: 100%;
          filter: brightness(0.7);
          transition: filter 0.5s;
        }
      }
    }
  }
}
