<ng-container *ngIf="isLoading; else elseTemplate">
  <app-loading-spinner></app-loading-spinner>
</ng-container>

<ng-template #elseTemplate>
  <div class="container">
    <app-breadcrumbs></app-breadcrumbs>
    <section class="blog-wrapper">
      <h2>{{ 'BLOG.ALL_ISSUES' | translate}}</h2>
      <div class="issues-wrapper">
        <a [routerLink]="['/issues', category?.path]"
           class="issue"
           *ngFor="let category of categories">
          <div class="img-wrapper">
            <picture>
              <img [src]="category?.image"
                   [alt]="category?.title">
            </picture>
          </div>
          <p class="title">{{ category?.title }}</p>
          <p class="description">{{ category?.description }}</p>
        </a>
      </div>
    </section>
  </div>
</ng-template>
