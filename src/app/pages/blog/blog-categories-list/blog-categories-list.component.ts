import { isPlatformBrowser } from '@angular/common';
import { Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslateService } from '@ngx-translate/core';
import { Menu } from 'Core/models';
import { MenuService, WINDOW } from 'Core/services';
import { MetafrenzyService } from 'ngx-metafrenzy';
import { finalize } from 'rxjs';

@UntilDestroy()
@Component({
  selector: 'app-blog-categories-list',
  templateUrl: './blog-categories-list.component.html',
  styleUrls: ['./blog-categories-list.component.scss'],
})
export class BlogCategoriesListComponent implements OnInit {
  categories!: Menu[];
  isLoading = false;
  currentCategories: string[] = [];

  get isBrowser() {
    return isPlatformBrowser(this.platformId);
  }

  constructor(
    private metaService: MetafrenzyService,
    private translateService: TranslateService,
    @Inject(PLATFORM_ID) private platformId: string,
    @Inject(WINDOW) private window: Window,
    private menuService: MenuService
  ) {
    if (this.isBrowser) {
      this.metaService.setTitle(
        this.translateService.instant('BLOG.PAGES_LIST_TITLE')
      );
      this.metaService.setMetaTag(
        'description',
        this.translateService.instant('BLOG.PAGES_LIST_DESCRIPTION')
      );
    }
  }

  ngOnInit(): void {
    if (this.isBrowser) {
      this.menuService.menu$.pipe(untilDestroyed(this)).subscribe(res => {
        if (res) {
          this.categories = res;
        } else {
          this.getCategories();
        }
      });
    }
  }

  getCategories() {
    this.isLoading = true;

    this.menuService
      .getMenu('blog-categories')
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: res => {
          this.categories = res;
          this.menuService.menu = res;
        },
      });
  }

  getCategoriesTitle(categoryId: number) {
    this.currentCategories = [];
    this.categories
      ?.find(res => res.id === categoryId)
      ?.children?.forEach(child => {
        this.currentCategories.push(
          this.categories.find(category => category.id === child.parent_id)
            ?.title!
        );
      });
  }
}
