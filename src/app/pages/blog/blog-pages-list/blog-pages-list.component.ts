import { isPlatformBrowser, isPlatformServer } from '@angular/common';
import { Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { Meta } from '@angular/platform-browser';
import { ActivatedRoute, Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslateService } from '@ngx-translate/core';
import { setTimeoutRX } from 'Core/helpers';
import { BreadCrumb, Menu, Page } from 'Core/models';
import { MenuService, PageService, WINDOW } from 'Core/services';
import { NgxMasonryOptions } from 'ngx-masonry';
import { MetafrenzyService } from 'ngx-metafrenzy';
import { finalize, take, tap } from 'rxjs';

@UntilDestroy()
@Component({
  selector: 'app-blog-pages-list',
  templateUrl: './blog-pages-list.component.html',
  styleUrls: ['./blog-pages-list.component.scss'],
})
export class BlogPagesListComponent implements OnInit {
  page?: Page;
  innerHtml?: string;
  breadCrumbs: BreadCrumb[] = [];
  metas: HTMLMetaElement[] = [];
  isLoading = false;
  categories!: Menu[] | undefined;
  lastIssues!: Menu[] | undefined;
  masonryOptions: NgxMasonryOptions = {
    horizontalOrder: true,
    originLeft: true,
  };
  isPageGot = false;
  isGotCategories = false;
  hashTagName!: string;

  get isBrowser() {
    return isPlatformBrowser(this.platformId);
  }

  get isServer() {
    return isPlatformServer(this.platformId);
  }

  get url() {
    return this.router.url;
  }

  get isMobile() {
    if (this.isBrowser) {
      if (this.window.innerWidth < 1000) {
        return true;
      } else {
        return false;
      }
    }
    return;
  }

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private pageService: PageService,
    private meta: Meta,
    private menuService: MenuService,
    private metaService: MetafrenzyService,
    private translateService: TranslateService,
    @Inject(WINDOW) private window: Window,
    @Inject(PLATFORM_ID) private platformId: string
  ) {}

  ngOnInit(): void {
    if (this.isBrowser) {
      this.menuService.menu$.pipe(untilDestroyed(this)).subscribe({
        next: res => {
          if (res?.length > 0) {
            const hashTag = this.route.snapshot.queryParams['hashtag'];
            if (this.router.url.slice(1) === '') {
              this.router.navigate(['/issues', res?.[0].path]);
            } else {
              this.getCategoryChildes(res);
              this.getLastIssues(res);
            }
            if (
              this.route.snapshot.url?.[0]?.path === 'search-by-tag' &&
              hashTag
            ) {
              this._getPostsByHashTag(hashTag, res);
              this.getLastIssues(res);
            }
          } else {
            if (!this.isGotCategories) {
              this.getCategories();
              this.isGotCategories = true;
            }
          }
        },
      });

      this.route.url.subscribe(res => {
        if (
          res?.[0]?.path !== 'error' &&
          this.router.url.slice(1) !== '' &&
          res?.[0]?.path !== 'search-by-tag'
        ) {
          this.getPageData(this.router.url.slice(8));
        }

        if (this.menuService.menu?.length > 0) {
          this.getCategoryChildes(this.menuService.menu);
          this.getLastIssues(this.menuService.menu);
        } else {
          if (!this.isGotCategories) {
            this.getCategories();
            this.isGotCategories = true;
          }
        }
      });
    }
  }

  getCategories() {
    this.isLoading = true;

    this.menuService
      .getMenu('blog-categories')
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: res => {
          if (this.router.url.slice(1) === '') {
            this.router.navigate(['/issues', res?.[0].path]);
          } else {
            this.getLastIssues(res);
            this.getCategoryChildes(res);
          }
          this.menuService.menu = res;
        },
      });
  }

  getLastIssues(res: Menu[]) {
    if (this.isMobile) {
      this.lastIssues = res
        .filter(value => value.path !== this.router.url.slice(8))
        .slice(0, 2);
    } else {
      this.lastIssues = res
        .filter(value => value.path !== this.router.url.slice(8))
        .slice(0, 3);
    }
  }

  getCategoryChildes(res: Menu[]) {
    const childCategories = res?.find(
      category => category.path === this.router.url.slice(8)
    )?.children;
    this.categories = childCategories;
    this.getLastIssues(res);
  }

  private _getPostsByHashTag(hashTag: string, res: Menu[]) {
    this.hashTagName = hashTag;
    let allChildren: Menu[] = [];
    const hashtagBreadCrumb: BreadCrumb = {
      id: 0,
      contentable_id: 'null',
      contentable_type: 'blog',
      title: hashTag,
      path: this.url.slice(1),
      preview_image: 'null',
      meta: null,
    };
    this.breadCrumbs = [...this.breadCrumbs, hashtagBreadCrumb];
    res.forEach(menuItem => {
      if (menuItem.children) {
        allChildren = [...allChildren, ...menuItem.children];
      }
    });
    const postsWithHashtag = allChildren.filter(post =>
      post.hash_tags.find(tag => tag.title === hashTag)
    );
    this.metaService.setTitle(
      hashTag +
        ' ' +
        postsWithHashtag.length +
        ' ' +
        this.translateService.instant('BLOG.RESULTS')
    );
    setTimeoutRX(() => {
      this.categories = postsWithHashtag;
    }, 0);
  }

  private getPageData(url: string) {
    this.isLoading = true;
    this.metaService.setTitle(this.translateService.instant('META_TAGS.PAGE'));
    this.pageService
      .getPagePreview(url)
      .pipe(
        take(1),
        tap(res => {
          this.isLoading = false;
          this.page = res;
          this.cleanMeta();

          this.metaService.setTitle(
            res.meta.find(item => item.key === 'title')?.value!
          );
          this.metaService.setMetaTag(
            'description',
            res.meta.find(item => item.key === 'description')?.value!
          );
          this.metaService.setMetaTag('og:url', this.window.location.href);
          this.metaService.setMetaTag(
            'Keywords',
            res.meta.find(item => item.key === 'keywords')?.value!
              ? res.meta.find(item => item.key === 'keywords')?.value!
              : ''
          );
        })
      )
      .subscribe({
        next: res => {
          this.getBody(res.id);
          this.getBreadcrumbs(res.id);
        },
        error: err => {
          if (err.status === 404) {
            this.router.navigateByUrl('error', {
              skipLocationChange: true,
            });
          }
        },
      });
  }

  private getBody(id: number) {
    this.isLoading = true;

    this.pageService
      .getPageBody(id)
      .pipe(
        tap(() => {
          this.isLoading = false;
        })
      )
      .subscribe(res => {
        this.innerHtml = res;
      });
  }

  private cleanMeta(): void {
    this.metas.forEach(el => {
      this.meta.removeTagElement(el);
    });
    this.metas = [];
  }

  private getBreadcrumbs(id: number) {
    this.pageService.getPageBreadcrumbs(id).subscribe(res => {
      this.breadCrumbs = res.reverse();
    });
  }
}
