<ng-container *ngIf="isLoading; else elseTemplate">
  <app-loading-spinner></app-loading-spinner>
</ng-container>

<ng-template #elseTemplate>
  <div class="container">
    <app-breadcrumbs [breadcrumbs]="breadCrumbs"></app-breadcrumbs>
    <div class="contnent-from-body-html" [innerHTML]="innerHtml"></div>
    <ng-container *ngIf="hashTagName">
      <h1 class="hashtag-name">{{ hashTagName }}</h1>
      <p class="found-results">{{ categories?.length + ' ' + ('BLOG.FOUND_RESULTS' | translate) }}</p>
    </ng-container>

    <section class="blog-wrapper">
      <ngx-masonry [options]="masonryOptions" class="issues-wrapper" [ordered]="true">
        <a ngxMasonryItem [routerLink]="['/issues/' + category?.path]" class="issue"
          *ngFor="let category of categories">
          <div class="img-wrapper">
            <img [src]="category?.image" [alt]="category?.title">
          </div>
          <p class="title">{{ category?.title }}</p>
          <p class="description">{{ category?.description }}</p>
          <p class="more-details">{{ 'BLOG.MORE_DETAILS' | translate }}</p>
        </a>
      </ngx-masonry>
      <aside class="last-month-wrapper" *ngIf="lastIssues?.length! > 1">
        <h2 class="title">{{ 'BLOG.LAST_ISSUES' |translate }}</h2>
        <div class="last-issues-wrapper">
          <a [routerLink]="['/issues', category?.path]" class="issue" *ngFor="let category of lastIssues">
            <div class="img-wrapper">
              <picture>
                <img [src]="category?.image" [alt]="category?.title">
              </picture>
            </div>
            <p class="title">{{ category?.title }}</p>
            <p class="description">{{ category?.description }}</p>
          </a>
        </div>
        <a routerLink="/issues" class="watch-all-issues">
          <b>
            <i></i>
            <i></i>
            <i></i>
            <i></i>
          </b>
          <span>{{ 'BLOG.WATCH_ALL_ISSUES' | translate}}</span>

        </a>
      </aside>
    </section>
  </div>
</ng-template>