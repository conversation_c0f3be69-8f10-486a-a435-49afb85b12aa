@use "core" as *;

.container {
  flex-direction: column;

  .issues-wrapper {
    .issue {
      width: 31%;
      margin: 1%;

      @include to(s) {
        width: 98%;
      }

      .img-wrapper {
        overflow: hidden;

        img {
          overflow: hidden;
          transition: transform 0.5s;
        }
      }

      .title {
        margin-top: 1.5rem;
        font-size: 2rem;
        font-weight: 500;
      }

      .description {
        margin-top: 0.5rem;
        font-size: 1.5rem;
        font-weight: 300;
        text-align: justify;
      }

      .more-details {
        margin-top: 1.5rem;
        font-size: 2rem;
        font-weight: 500;

        &::after {
          content: "\27A4";
          margin-left: 0.5rem;
        }
      }

      &:hover {
        .img-wrapper {
          img {
            transform: scale(1.03);
          }
        }
      }
    }
  }

  .last-month-wrapper {
    padding: 2rem 0 1rem;

    .title {
      margin-bottom: 3rem;
      font-size: 4rem;
      font-weight: 600;
      text-align: center;

      @include to(s) {
        font-size: 2.4rem;
      }
    }

    .last-issues-wrapper {
      display: flex;
      flex-wrap: wrap;
      margin: -1%;

      .issue {
        position: relative;
        flex: 0 0 30.3%;
        margin: 1.5%;
        overflow: hidden;
        transition: transform 0.5s;
        will-change: transform;

        @include to(l) {
          flex: 0 0 47%;
        }

        &:hover {
          transform: scale(1.02);

          .img-wrapper {
            filter: brightness(0.9);
          }
        }

        .title {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 1;
          width: 100%;
          padding-top: 27%;
          padding-bottom: 34.5%;
          font-size: 1.4rem;
          text-align: center;
          color: #fff;

          @include to(s) {
            font-size: 0.9rem;
          }
        }

        .description {
          position: absolute;
          bottom: 12%;
          left: 0;
          z-index: 1;
          width: 100%;
          font-size: 3rem;
          line-height: 1;
          font-weight: 500;
          text-align: center;
          color: #fff;

          @include to(s) {
            font-size: 1.8rem;
          }
        }

        .img-wrapper {
          width: 100%;
          height: 100%;
          filter: brightness(0.7);
          transition: filter 0.5s;
        }
      }
    }

    .watch-all-issues {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 3.5rem 0 1rem;

      b {
        display: flex;
        flex-wrap: wrap;
        width: 4.4rem;
        margin-bottom: 0.5rem;

        &:hover {
          i {
            background-color: #6a533d;
            border-color: transparent;
            border-radius: 18%;
          }
        }

        i {
          width: 18px;
          height: 22px;
          margin: 2px;
          border: 2px solid #6a533d;
          transition: 0.18s linear;
        }
      }
    }
  }

  .hashtag-name {
    margin-bottom: 1rem;
    font-size: 5rem;
    font-weight: bold;
    text-align: center;

    @include to(s) {
      font-size: 3rem;
    }
  }

  .found-results {
    margin-bottom: 1.6rem;
    font-size: 2rem;
    font-weight: 600;
    text-align: center;

    @include to(s) {
      font-size: 1.8rem;
    }
  }
}
