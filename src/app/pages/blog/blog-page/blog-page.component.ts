import { isPlatformBrowser } from '@angular/common';
import { Component, Inject, OnInit, PLATFORM_ID } from '@angular/core';
import { Meta } from '@angular/platform-browser';
import { ActivatedRoute, Event, NavigationEnd, Router } from '@angular/router';
import { UntilDestroy, untilDestroyed } from '@ngneat/until-destroy';
import { TranslateService } from '@ngx-translate/core';
import {
  BreadCrumb,
  HashtagResponse,
  Menu,
  Page,
  SocialMediaSharingLinks,
} from 'Core/models';
import { MenuService, PageService, WINDOW } from 'Core/services';
import { MetafrenzyService } from 'ngx-metafrenzy';
import { finalize, tap } from 'rxjs';
@UntilDestroy()
@Component({
  selector: 'app-blog-page',
  templateUrl: './blog-page.component.html',
  styleUrls: ['./blog-page.component.scss'],
})
export class BlogPageComponent implements OnInit {
  page?: Page;
  innerHtml?: string;
  breadCrumbs: BreadCrumb[] = [];
  metas: HTMLMetaElement[] = [];
  isLoading = false;
  lastPosts!: Menu[];
  currentPost!: Menu;
  categories!: Menu[];
  shareLinks!: SocialMediaSharingLinks;

  get isBrowser() {
    return isPlatformBrowser(this.platformId);
  }

  get url() {
    return this.router.url;
  }

  get isMobile() {
    if (this.window.innerWidth < 1000) {
      return true;
    } else {
      return false;
    }
  }

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private pageService: PageService,
    private meta: Meta,
    private menuService: MenuService,
    private metaService: MetafrenzyService,
    private translateService: TranslateService,
    @Inject(WINDOW) private window: Window,
    @Inject(PLATFORM_ID) private platformId: string
  ) {}

  ngOnInit(): void {
    if (this.isBrowser) {
      this.menuService.menu$.pipe(untilDestroyed(this)).subscribe({
        next: res => {
          if (res?.length > 0) {
            this.getLastPosts(res);
            this.getCurrentPost(res);
          } else {
            this.getCategories();
          }
        },
      });

      this.route.url.subscribe(res => {
        console.log(res?.[0].path);

        if (res?.[0].path !== 'error') {
          this.getPageData(this.router.url.slice(8));
        }
      });

      this.router.events.subscribe((event: Event) => {
        if (event instanceof NavigationEnd) {
          this.getLastPosts(this.menuService.menu);
        }
      });
    }
  }

  changePost(path: string) {
    this.router.navigateByUrl(`/issues/${path}`);
  }

  getCategories() {
    this.isLoading = true;

    this.menuService
      .getMenu('blog-categories')
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: res => {
          this.getLastPosts(res);
          this.menuService.menu = res;
        },
      });
  }

  getCurrentPost(res: Menu[]) {
    this.currentPost = res
      ?.find(menu => menu.path === this.router.url.split('/')[2])
      ?.children?.find(child =>
        child.path?.includes(this.router.url.split('/')[3])
      )!;
    this.getShareLinks();
  }

  getShareLinks() {
    this.shareLinks = {
      vk: `https://vk.com/share.php?url=${this.window.location.href}`,
      ok: `https://connect.ok.ru/offer?url=${this.window.location.href}&title=${this.page?.title}&imageUrl=${this.currentPost?.image}`,
      fb: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        this.window.location.href
      )}`,
    };
  }

  getLastPosts(res: Menu[]) {
    if (this.isMobile) {
      this.lastPosts = res
        .find(res => res.path === this.router.url.split('/')[2])
        ?.children?.filter(
          post => !post.path?.includes(this.router.url.split('/')[3])
        )
        .slice(0, 2)!;
    } else {
      this.lastPosts = res
        ?.find(res => res.path === this.router.url.split('/')[2])
        ?.children?.filter(
          post => !post.path?.includes(this.router.url.split('/')[3])
        )
        .slice(0, 3)!;
    }
  }

  openTagPosts(tag: HashtagResponse) {
    this.router.navigate(['/search-by-tag'], {
      queryParams: { hashtag: tag.title },
    });
  }

  private getPageData(url: string) {
    this.isLoading = true;
    this.metaService.setTitle(this.translateService.instant('META_TAGS.PAGE'));
    this.pageService
      .getPagePreview(url)
      .pipe(
        tap(res => {
          this.page = res;

          this.cleanMeta();

          this.metaService.setTitle(
            res.meta.find(item => item.key === 'title')?.value!
          );
          this.metaService.setMetaTag(
            'description',
            res.meta.find(item => item.key === 'description')?.value!
          );
          this.metaService.setMetaTag('og:url', this.window.location.href);
          this.metaService.setMetaTag(
            'Keywords',
            res.meta.find(item => item.key === 'keywords')?.value!
              ? res.meta.find(item => item.key === 'keywords')?.value!
              : ''
          );
        }),
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe({
        next: res => {
          this.getBody(res.id);
          this.getBreadcrumbs(res.id);
        },
        error: err => {
          if (err.status === 404) {
            this.router.navigateByUrl('error', {
              skipLocationChange: true,
            });
          }
        },
      });
  }

  private getBody(id: number) {
    this.isLoading = true;

    this.pageService
      .getPageBody(id)
      .pipe(
        finalize(() => {
          this.isLoading = false;
        })
      )
      .subscribe(res => {
        this.innerHtml = res;
      });
  }

  private cleanMeta(): void {
    this.metas.forEach(el => {
      this.meta.removeTagElement(el);
    });
    this.metas = [];
  }

  private getBreadcrumbs(id: number) {
    this.pageService.getPageBreadcrumbs(id).subscribe(res => {
      this.breadCrumbs = res;
    });
  }
}
