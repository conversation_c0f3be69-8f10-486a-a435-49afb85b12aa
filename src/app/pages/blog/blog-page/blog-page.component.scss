@use "core" as *;

.container {
  display: flex;
  flex-direction: column;
  padding-bottom: 3.1rem;

  @include to(xl) {
    padding: 2rem 0;
    padding-bottom: 3.1rem;
    background-color: #fff;
  }

  @include to(s) {
    padding-top: 0;
    padding-bottom: 1.6rem;
  }

  .last-month-wrapper {
    padding: 2rem 0 1rem;

    .title {
      margin-bottom: 3rem;
      font-size: 4rem;
      font-weight: 600;
      text-align: center;

      @include to(s) {
        font-size: 2.4rem;
      }
    }

    .last-issues-wrapper {
      display: flex;
      flex-wrap: wrap;
      margin: -1%;
    }
  }

  .issue {
    display: flex;
    flex: 0 0 30.3%;
    flex-direction: column;
    margin: 1.5%;
    overflow: hidden;
    transition: transform 0.5s;
    will-change: transform;

    @include to(l) {
      flex: 0 0 47%;
    }

    &:hover {
      .img-wrapper {
        img {
          transform: scale(1.02);
        }
      }
    }

    .title {
      margin-bottom: 1rem;
      font-size: 3rem;
      text-align: left;

      @include to(s) {
        font-size: 2rem;
      }
    }

    .more-details {
      font-size: 1.5rem;
      font-weight: 500;

      &::after {
        content: "\27A4";
        margin-left: 0.5rem;
      }
    }

    .img-wrapper {
      display: flex;
      width: 100%;
      height: 38.5rem;
      margin-bottom: 1rem;

      @include to(s) {
        height: 19.5rem;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s;
      }
    }
  }

  .social-media-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 4rem;
  }

  .social-media-title {
    margin-bottom: 1rem;
    font-size: 1.8rem;
    line-height: 1.2;
    font-weight: bold;

    @include to(s) {
      margin-bottom: 2rem;
    }
  }

  .social-bar {
    display: flex;
    align-items: center;
    padding-left: 0.5rem;

    @include to(s) {
      justify-content: center;
      width: 100%;
      padding: 0;
    }

    a {
      @include to(s) {
        flex: 0 0 20%;
      }

      &:not(:last-child) {
        margin-right: 3.2rem;

        @include to(s) {
          margin-right: 0;
        }
      }
    }

    .social-bar-item {
      width: 6rem;
      height: 6rem;
      background-repeat: no-repeat;
      background-position: center;
      background-size: 6rem 6rem;

      &.vk {
        background-image: url("/assets/img/footer-vk-share-icon.svg");
      }

      &.facebook {
        background-image: url("/assets/img/footer-facebook-share-icon.svg");
      }

      &.ok {
        background-image: url("/assets/img/footer-ok-share-icon.svg");
      }
    }
  }

  .tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    margin-top: 2rem;
    margin-bottom: 4rem;

    .tag {
      display: flex;
      flex-shrink: 0;
      justify-content: center;
      align-items: center;
      margin: 0 0.2rem 0.8rem;
      padding: 1.5rem;
      font-size: 2rem;
      background-color: #e1d5c4;
      cursor: pointer;
      transition: background 300ms ease, color 300ms ease;

      &:hover {
        color: $white;
        background-color: #6a533d;
      }
    }
  }
}
