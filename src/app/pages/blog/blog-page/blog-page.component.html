<ng-container *ngIf="isLoading; else elseTemplate">
  <app-loading-spinner></app-loading-spinner>
</ng-container>

<ng-template #elseTemplate>
  <div class="container" #scrollEl>
    <article class="left-wrapper">
      <app-breadcrumbs [breadcrumbs]="breadCrumbs"></app-breadcrumbs>
      <ul class="blog-post-pages-list">
        <li class="blog-post-page-list-item" [innerHTML]="innerHtml! | safeHtml">
        </li>
      </ul>
    </article>
    <ul class="tags" *ngIf="page?.hash_tags?.length! > 0">
      <li class="tag" *ngFor="let tag of page?.hash_tags" (click)="openTagPosts(tag)">{{ tag.title }}</li>
    </ul>
    <div class="social-media-wrapper">
      <p class="social-media-title">{{ 'BLOG.SHARE' | translate }}</p>
      <ul class="social-bar">
        <a [href]="shareLinks?.vk" target="_blank" aria-label="VK" rel="nofollow">
          <li class="social-bar-item vk"></li>
        </a>
        <a [href]="shareLinks?.fb" target="_blank" aria-label="Facebook" rel="nofollow">
          <li class="social-bar-item facebook"></li>
        </a>
        <a [href]="shareLinks?.ok" target="_blank" aria-label="OK" rel="nofollow">
          <li class="social-bar-item ok"></li>
        </a>
      </ul>
    </div>
    <aside class="last-month-wrapper" *ngIf="lastPosts?.length! > 1">
      <h2 class="title">{{ 'BLOG.MORE_POSTS' |translate }}</h2>
      <div class="last-issues-wrapper">
        <a (click)="changePost(post?.path!)" class="issue" *ngFor="let post of lastPosts">
          <div class="img-wrapper">
            <picture>
              <img [src]="post?.image" [alt]="post?.title">
            </picture>
          </div>
          <p class="title">{{ post?.title }}</p>
          <p class="more-details">{{ 'BLOG.MORE_DETAILS' | translate }}</p>
        </a>
      </div>
    </aside>
  </div>
</ng-template>