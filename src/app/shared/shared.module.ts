import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { NgxMasonryModule } from 'ngx-masonry';
import { ComponentsModule } from './components/components.module';
import { DirectivesModule } from './directives/directives.module';
import { ModulesModule } from './modules/modules.module';
import { PipesModule } from './pipes/pipes.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    ComponentsModule,
    TranslateModule,
    PipesModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    NgxMasonryModule,
  ],
  exports: [
    ComponentsModule,
    PipesModule,
    DirectivesModule,
    ModulesModule,
    TranslateModule,
    PipesModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    NgxMasonryModule,
  ],
})
export class SharedModule {}
