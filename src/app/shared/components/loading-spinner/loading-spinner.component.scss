.container {
  display: flex;
  flex: 1;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.loader,
.loader::after {
  width: 10em;
  height: 10em;
  border-radius: 50%;
}

.loader {
  position: relative;
  margin: 10rem auto;
  font-size: 10px;
  text-indent: -9999em;
  border-top: 1.1em solid rgba(100, 216, 85, 0.2);
  border-right: 1.1em solid rgba(100, 216, 85, 0.2);
  border-bottom: 1.1em solid rgba(100, 216, 85, 0.2);
  border-left: 1.1em solid #6f7e0d;
  transform: translateZ(0);
  animation: load8 1.1s infinite linear;
}

@keyframes load8 {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
