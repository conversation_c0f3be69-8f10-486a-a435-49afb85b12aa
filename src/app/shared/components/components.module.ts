import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { BreadcrumbsComponent } from './breadcrumbs/breadcrumbs.component';
import { LoadingSpinnerComponent } from './loading-spinner/loading-spinner.component';

@NgModule({
  declarations: [LoadingSpinnerComponent, BreadcrumbsComponent],
  imports: [CommonModule, TranslateModule, ReactiveFormsModule, RouterModule],
  exports: [LoadingSpinnerComponent, BreadcrumbsComponent],
})
export class ComponentsModule {}
