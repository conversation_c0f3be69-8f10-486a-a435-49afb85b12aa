import { Component, Inject, Input } from '@angular/core';
import { Router } from '@angular/router';
import { <PERSON><PERSON><PERSON><PERSON> } from '@ngneat/until-destroy';
import { BreadCrumb } from 'Core/models';
import { WINDOW } from 'Core/services';

@UntilDestroy()
@Component({
  selector: 'app-breadcrumbs',
  templateUrl: './breadcrumbs.component.html',
  styleUrls: ['./breadcrumbs.component.scss'],
})
export class BreadcrumbsComponent {
  @Input() breadcrumbs!: BreadCrumb[];
  @Input() breadcrumbsColor!: string;

  constructor(@Inject(WINDOW) private window: Window, private router: Router) {}

  navigateByUrl(path: string) {
    if (path.includes('search-by-tag')) {
      return;
    }
    if (this.router.url.slice(8) === path) {
      return;
    }

    this.router.navigate(['/issues', path]);
  }
}
