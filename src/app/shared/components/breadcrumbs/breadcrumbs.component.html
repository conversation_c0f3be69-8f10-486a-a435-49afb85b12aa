<div class="breadcrumbs">
  <a routerLink="/"
     [style.color]="breadcrumbsColor ? breadcrumbsColor : '#000'">{{ 'BREADCRUMBS.MAIN' | translate }}</a>
  <a routerLink="/issues"
     [style.color]="breadcrumbsColor ? breadcrumbsColor : '#000'">{{ 'BLOG.ALL_ISSUES' | translate }}</a>
  <a *ngFor="let breadcrumb of breadcrumbs"
     [style.color]="breadcrumbsColor ? breadcrumbsColor : '#000'"
     (click)="navigateByUrl(breadcrumb.path)">
    {{ breadcrumb.title || breadcrumb.path }}
  </a>
</div>
