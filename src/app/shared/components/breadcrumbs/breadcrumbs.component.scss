@use "core" as *;

.breadcrumbs {
  display: flex;
  flex-wrap: wrap;
  padding: 1.6rem 0.5rem 1.6rem 0;

  @include to(l) {
    padding-left: 2rem;
  }

  @include to(s) {
    padding-left: 1.6rem;
  }

  a {
    font-size: 1.3rem;
    color: #2d2d2d;
    cursor: pointer;

    &:not(:first-child) {
      margin-left: 0.5rem;

      &::before {
        content: ">";
        margin-right: 0.5rem;
        color: inherit;
      }
    }
  }
}
