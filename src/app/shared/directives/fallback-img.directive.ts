import { LocationStrategy } from '@angular/common';
import {
  Directive,
  ElementRef,
  HostListener,
  Input,
  OnInit,
  Renderer2,
} from '@angular/core';

type ImageType = 'default';

@Directive({
  selector: '[appFallbackImg]',
})
export class FallbackImgDirective implements OnInit {
  @Input() appFallbackImg: ImageType = 'default';
  readonly images = new Map<ImageType, string>([
    ['default', 'assets/img/fallback-icon.svg'],
  ]);
  hasError = false;
  private fallbackImgSrc!: string;

  get el() {
    return this.elRef.nativeElement;
  }

  constructor(
    private elRef: ElementRef<HTMLImageElement>,
    private renderer: Renderer2,
    private locationStrategy: LocationStrategy
  ) {}

  @HostListener('error')
  onError() {
    if (!this.hasError) {
      this.hasError = true;
      this.applyFallback();
    }
  }

  ngOnInit(): void {
    if (this.images.has(this.appFallbackImg)) {
      this.fallbackImgSrc = this.images.get(this.appFallbackImg)!;
    } else {
      return console.warn('Fallback image not provided');
    }

    if (!this.el.src) {
      this.applyFallback();
    }
  }

  private applyFallback() {
    if (!this.fallbackImgSrc) {
      return;
    }

    this.renderer.setAttribute(
      this.el,
      'src',
      `${this.locationStrategy.getBaseHref()}${this.fallbackImgSrc}`
    );
  }
}
