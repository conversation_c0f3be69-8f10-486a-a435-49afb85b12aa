import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { ClickOutsideDirective } from './click-outside.directive';
import { ClickPreventDefaultDirective } from './click-prevent-default.directive';
import { ClickStopPropagationDirective } from './click-stop-propagation.directive';
import { DisabledControlDirective } from './disabled-control.directive';
import { ExternalLinkDirective } from './external-link.directive';
import { FallbackImgDirective } from './fallback-img.directive';

@NgModule({
  declarations: [
    ExternalLinkDirective,
    ClickStopPropagationDirective,
    FallbackImgDirective,
    DisabledControlDirective,
    ClickOutsideDirective,
    ClickPreventDefaultDirective,
  ],
  imports: [CommonModule],
  exports: [
    ExternalLinkDirective,
    ClickStopPropagationDirective,
    FallbackImgDirective,
    DisabledControlDirective,
    ClickOutsideDirective,
    ClickPreventDefaultDirective,
  ],
})
export class DirectivesModule {}
