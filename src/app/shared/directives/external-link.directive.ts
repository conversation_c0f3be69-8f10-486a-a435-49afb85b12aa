import {
  Directive,
  ElementRef,
  Input,
  OnChanges,
  Optional,
} from '@angular/core';
import { NavigationEnd, Router, RouterLinkWithHref } from '@angular/router';
import { filter } from 'rxjs/operators';
import { setTimeoutRX } from 'Core/helpers';

@Directive({
  selector: '[appExternalLink]',
})
export class ExternalLinkDirective implements OnChanges {
  @Input() appExternalLink?: string;

  constructor(
    @Optional() private link: RouterLinkWithHref,
    private el: ElementRef<HTMLAnchorElement>,
    private router: Router
  ) {
    this.router.events
      .pipe(filter(e => e instanceof NavigationEnd))
      // eslint-disable-next-line @angular-eslint/no-lifecycle-call
      .subscribe(() => this.ngOnChanges());
  }

  ngOnChanges() {
    if (!this.link || !this.appExternalLink) {
      return;
    }

    this.el.nativeElement.href = this.link.href = this.appExternalLink;

    setTimeoutRX(() => {
      this.el.nativeElement.target = '_blank';
    }, 0);

    this.el.nativeElement.rel = 'noopener nofollow';

    // Replace onClick
    this.link.onClick = (...args: any[]) => {
      return true;
    };
  }
}
