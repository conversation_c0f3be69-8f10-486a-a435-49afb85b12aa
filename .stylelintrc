{"extends": ["stylelint-config-prettier"], "plugins": ["stylelint-order", "stylelint-scss"], "customSyntax": "postcss-scss", "rules": {"alpha-value-notation": "number", "at-rule-disallowed-list": ["debug"], "at-rule-empty-line-before": ["always", {"except": ["blockless-after-same-name-blockless", "first-nested"], "ignore": ["after-comment"]}], "at-rule-no-unknown": null, "at-rule-no-vendor-prefix": true, "block-no-empty": true, "color-function-notation": "legacy", "color-hex-length": "short", "color-named": "never", "color-no-invalid-hex": true, "comment-no-empty": true, "comment-whitespace-inside": "always", "comment-word-disallowed-list": ["/[^a-zA-Z0-9_`~=!@%&#<>:;,'\"\\\\/\\$\\*\\?\\^\\-\\.\\+\\(\\)\\s\\[\\]\\|\\{\\}]+/", {"message": "Comments must be in English and contain valid characters"}], "declaration-block-single-line-max-declarations": 1, "declaration-empty-line-before": ["always", {"except": ["after-declaration", "first-nested"], "ignore": ["after-comment"]}], "declaration-no-important": true, "declaration-property-unit-allowed-list": {"line-height": []}, "declaration-property-value-disallowed-list": {"border": ["none"], "border-bottom": ["none"], "border-left": ["none"], "border-right": ["none"], "border-top": ["none"], "outline": ["none"]}, "font-family-name-quotes": "always-unless-keyword", "font-family-no-duplicate-names": true, "font-family-no-missing-generic-family-keyword": true, "font-weight-notation": "named-where-possible", "function-calc-no-unspaced-operator": true, "function-url-quotes": "always", "keyframe-declaration-no-important": true, "length-zero-no-unit": true, "max-nesting-depth": [5, {"ignore": ["blockless-at-rules", "pseudo-classes"]}], "media-feature-name-no-vendor-prefix": true, "media-feature-range-operator-space-after": "always", "media-feature-range-operator-space-before": "always", "no-duplicate-at-import-rules": true, "no-duplicate-selectors": true, "number-max-precision": 2, "order/order": [["custom-properties", "dollar-variables", {"name": "extend", "type": "at-rule"}, {"hasBlock": false, "name": "include", "type": "at-rule"}, "declarations", {"hasBlock": true, "name": "include", "type": "at-rule"}, "rules"]], "order/properties-order": [["content", "position", "top", "right", "bottom", "left", "z-index", "display", "-webkit-flex", "-ms-flex", "flex", "-webkit-flex-grow", "flex-grow", "-webkit-flex-shrink", "flex-shrink", "-webkit-flex-basis", "flex-basis", "-webkit-flex-flow", "flex-flow", "-webkit-flex-direction", "-ms-flex-direction", "flex-direction", "-webkit-flex-wrap", "flex-wrap", "-webkit-justify-content", "justify-content", "-webkit-align-content", "align-content", "-webkit-align-items", "align-items", "-webkit-order", "-ms-flex-order", "order", "-ms-grid-row-align", "-webkit-align-self", "align-self", "-ms-grid-column-align", "justify-self", "grid", "grid-area", "grid-auto-columns", "grid-auto-flow", "grid-auto-rows", "grid-column", "grid-column-end", "-ms-grid-column-span", "grid-column-gap", "-ms-grid-column", "grid-column-start", "grid-gap", "grid-row", "grid-row-end", "-ms-grid-row-span", "grid-row-gap", "-ms-grid-row", "grid-row-start", "grid-template", "grid-template-areas", "-ms-grid-columns", "grid-template-columns", "-ms-grid-rows", "grid-template-rows", "float", "clear", "-webkit-box-sizing", "-moz-box-sizing", "box-sizing", "width", "min-width", "max-width", "height", "min-height", "max-height", "margin", "margin-top", "margin-right", "margin-bottom", "margin-left", "padding", "padding-top", "padding-right", "padding-bottom", "padding-left", "overflow", "-ms-overflow-x", "overflow-x", "-ms-overflow-y", "overflow-y", "-webkit-overflow-scrolling", "-ms-overflow-style", "list-style", "list-style-position", "list-style-type", "list-style-image", "border-collapse", "border-spacing", "table-layout", "empty-cells", "caption-side", "font", "font-family", "font-size", "line-height", "font-weight", "vertical-align", "text-align", "direction", "color", "text-transform", "text-decoration", "font-style", "font-variant", "font-size-adjust", "font-stretch", "font-effect", "font-emphasize", "font-emphasize-position", "font-emphasize-style", "-webkit-font-smoothing", "-moz-osx-font-smoothing", "font-smooth", "-webkit-text-align-last", "-moz-text-align-last", "-ms-text-align-last", "text-align-last", "letter-spacing", "word-spacing", "white-space", "text-emphasis", "text-emphasis-color", "text-emphasis-style", "text-emphasis-position", "text-indent", "-ms-text-justify", "text-justify", "-ms-writing-mode", "text-outline", "text-wrap", "-ms-text-overflow", "text-overflow", "text-overflow-ellipsis", "text-overflow-mode", "text-orientation", "-ms-word-wrap", "word-wrap", "-ms-word-break", "word-break", "-moz-tab-size", "-o-tab-size", "overflow-wrap", "tab-size", "-webkit-hyphens", "-moz-hyphens", "hyphens", "unicode-bidi", "columns", "column-count", "column-fill", "column-gap", "column-rule", "column-rule-color", "column-rule-style", "column-rule-width", "column-span", "column-width", "text-shadow", "page-break-after", "page-break-before", "page-break-inside", "src", "background", "background-color", "background-image", "background-repeat", "background-position", "-ms-background-position-x", "background-position-x", "-ms-background-position-y", "background-position-y", "-webkit-background-size", "-moz-background-size", "-o-background-size", "background-size", "-webkit-background-clip", "-moz-background-clip", "background-clip", "background-origin", "background-attachment", "box-decoration-break", "background-blend-mode", "border", "border-width", "border-style", "border-color", "border-top", "border-top-width", "border-top-style", "border-top-color", "border-right", "border-right-width", "border-right-style", "border-right-color", "border-bottom", "border-bottom-width", "border-bottom-style", "border-bottom-color", "border-left", "border-left-width", "border-left-style", "border-left-color", "-webkit-border-radius", "-moz-border-radius", "border-radius", "-webkit-border-top-left-radius", "-moz-border-radius-topleft", "border-top-left-radius", "-webkit-border-top-right-radius", "-moz-border-radius-topright", "border-top-right-radius", "-webkit-border-bottom-right-radius", "-moz-border-radius-bottomright", "border-bottom-right-radius", "-webkit-border-bottom-left-radius", "-moz-border-radius-bottomleft", "border-bottom-left-radius", "-webkit-border-image", "-moz-border-image", "-o-border-image", "border-image", "-webkit-border-image-source", "-moz-border-image-source", "-o-border-image-source", "border-image-source", "-webkit-border-image-slice", "-moz-border-image-slice", "-o-border-image-slice", "border-image-slice", "-webkit-border-image-width", "-moz-border-image-width", "-o-border-image-width", "border-image-width", "-webkit-border-image-outset", "-moz-border-image-outset", "-o-border-image-outset", "border-image-outset", "-webkit-border-image-repeat", "-moz-border-image-repeat", "-o-border-image-repeat", "border-image-repeat", "outline", "outline-width", "outline-style", "outline-color", "outline-offset", "-webkit-box-shadow", "-moz-box-shadow", "box-shadow", "-webkit-transform", "-moz-transform", "-ms-transform", "-o-transform", "transform", "-webkit-transform-origin", "-moz-transform-origin", "-ms-transform-origin", "-o-transform-origin", "transform-origin", "-webkit-backface-visibility", "-moz-backface-visibility", "backface-visibility", "-webkit-perspective", "-moz-perspective", "perspective", "-webkit-perspective-origin", "-moz-perspective-origin", "perspective-origin", "-webkit-transform-style", "-moz-transform-style", "transform-style", "visibility", "cursor", "opacity", "interpolation-mode", "-webkit-filter", "filter", "backdrop-filter", "-webkit-transition", "-moz-transition", "-ms-transition", "-o-transition", "transition", "-webkit-transition-delay", "-moz-transition-delay", "-ms-transition-delay", "-o-transition-delay", "transition-delay", "-webkit-transition-timing-function", "-moz-transition-timing-function", "-ms-transition-timing-function", "-o-transition-timing-function", "transition-timing-function", "-webkit-transition-duration", "-moz-transition-duration", "-ms-transition-duration", "-o-transition-duration", "transition-duration", "-webkit-transition-property", "-moz-transition-property", "-ms-transition-property", "-o-transition-property", "transition-property", "-webkit-animation", "-moz-animation", "-ms-animation", "-o-animation", "animation", "-webkit-animation-name", "-moz-animation-name", "-ms-animation-name", "-o-animation-name", "animation-name", "-webkit-animation-duration", "-moz-animation-duration", "-ms-animation-duration", "-o-animation-duration", "animation-duration", "-webkit-animation-play-state", "-moz-animation-play-state", "-ms-animation-play-state", "-o-animation-play-state", "animation-play-state", "-webkit-animation-timing-function", "-moz-animation-timing-function", "-ms-animation-timing-function", "-o-animation-timing-function", "animation-timing-function", "-webkit-animation-delay", "-moz-animation-delay", "-ms-animation-delay", "-o-animation-delay", "animation-delay", "-webkit-animation-iteration-count", "-moz-animation-iteration-count", "-ms-animation-iteration-count", "-o-animation-iteration-count", "animation-iteration-count", "-webkit-animation-direction", "-moz-animation-direction", "-ms-animation-direction", "-o-animation-direction", "animation-direction", "-webkit-animation-fill-mode", "-moz-animation-fill-mode", "-ms-animation-fill-mode", "-o-animation-fill-mode", "animation-fill-mode", "appearance", "clip", "clip-path", "counter-reset", "counter-increment", "resize", "-webkit-user-select", "-moz-user-select", "-ms-user-select", "user-select", "-webkit-tap-highlight-color", "nav-index", "nav-up", "nav-right", "nav-down", "nav-left", "pointer-events", "quotes", "touch-action", "will-change", "zoom", "fill", "fill-rule", "clip-rule", "stroke"], {"severity": "warning"}], "property-no-unknown": true, "property-no-vendor-prefix": true, "rule-empty-line-before": ["always-multi-line", {"except": ["first-nested", "after-single-line-comment"], "ignore": ["after-comment"]}], "scss/at-else-if-parentheses-space-before": "always", "scss/at-extend-no-missing-placeholder": true, "scss/at-function-pattern": "^[a-z]+([a-z0-9-]+[a-z0-9]+)?$", "scss/at-if-no-null": true, "scss/at-import-no-partial-leading-underscore": true, "scss/at-import-partial-extension-blacklist": ["scss"], "scss/at-mixin-argumentless-call-parentheses": "never", "scss/at-mixin-pattern": "^[a-z]+([a-z0-9-]+[a-z0-9]+)?$", "scss/at-rule-conditional-no-parentheses": true, "scss/at-rule-no-unknown": true, "scss/comment-no-empty": true, "scss/dollar-variable-colon-space-after": "always", "scss/dollar-variable-colon-space-before": "never", "scss/dollar-variable-empty-line-after": ["always", {"except": ["last-nested", "before-dollar-variable"], "ignore": ["before-comment"]}], "scss/dollar-variable-empty-line-before": ["always", {"except": ["first-nested", "after-dollar-variable"], "ignore": ["after-comment"]}], "scss/double-slash-comment-whitespace-inside": "always", "scss/map-keys-quotes": "always", "scss/no-duplicate-dollar-variables": [true, {"ignoreInside": ["at-rule"], "ignoreInsideAtRules": ["if", "each", "mixin"]}], "scss/no-duplicate-mixins": true, "scss/operator-no-unspaced": true, "scss/percent-placeholder-pattern": "^[a-z]+[a-z0-9-]+?$", "scss/selector-no-redundant-nesting-selector": true, "selector-attribute-quotes": "always", "selector-class-pattern": "^[a-z]+[0-9]?([a-z0-9_-]+[a-z0-9]+)?$", "selector-max-compound-selectors": 5, "selector-max-id": 0, "selector-no-qualifying-type": [true, {"ignore": ["attribute", "class"]}], "selector-no-vendor-prefix": true, "selector-pseudo-element-colon-notation": "double", "selector-pseudo-element-no-unknown": [true, {"ignorePseudoElements": ["ng-deep"]}], "selector-type-no-unknown": [true, {"ignore": ["custom-elements"]}], "shorthand-property-no-redundant-values": true, "string-no-newline": true, "time-min-milliseconds": 100, "unit-disallowed-list": ["mm", "cm", "in", "pc"], "unit-no-unknown": true, "value-keyword-case": "lower", "value-no-vendor-prefix": true}}